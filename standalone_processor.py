"""
Standalone Enterprise KG Processor

This module provides a standalone implementation that can run independently
without CocoIndex dependencies. It processes documents and extracts entities
and relationships directly using LLM APIs and stores them in Neo4j.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import asdict

# Import our constants and schemas
from constants.schemas import DocumentSummary, EntityRelationship, ProcessingMetadata
from storage.neo4j_client import Neo4jClient, Neo4jConnection
from prompt_generator import PromptGenerator, create_full_prompt_generator


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LLMClient:
    """
    Simple LLM client that can work with different providers.
    This replaces the CocoIndex LLM integration for standalone usage.
    """

    def __init__(self, provider: str = "openai", model: str = "gpt-4o", api_key: Optional[str] = None):
        """
        Initialize LLM client.

        Args:
            provider: LLM provider (openai, anthropic, etc.)
            model: Model name
            api_key: API key (will try to get from environment if not provided)
        """
        self.provider = provider.lower()
        self.model = model
        self.api_key = api_key or self._get_api_key()
        self.client = self._initialize_client()

    def _get_api_key(self) -> Optional[str]:
        """Get API key from environment variables."""
        if self.provider == "openai":
            return os.getenv("OPENAI_API_KEY")
        elif self.provider == "anthropic":
            return os.getenv("ANTHROPIC_API_KEY")
        elif self.provider == "gemini":
            return os.getenv("GEMINI_API_KEY")
        elif self.provider == "openrouter":
            return os.getenv("OPENROUTER_API_KEY")
        return None

    def _initialize_client(self):
        """Initialize the appropriate LLM client."""
        if self.provider == "openai":
            try:
                import openai
                return openai.OpenAI(api_key=self.api_key)
            except ImportError:
                logger.error("OpenAI package not installed. Install with: pip install openai")
                return None
        elif self.provider == "anthropic":
            try:
                import anthropic
                return anthropic.Anthropic(api_key=self.api_key)
            except ImportError:
                logger.error("Anthropic package not installed. Install with: pip install anthropic")
                return None
        elif self.provider == "openrouter":
            try:
                import openai
                # OpenRouter uses OpenAI-compatible API
                return openai.OpenAI(
                    api_key=self.api_key,
                    base_url="https://openrouter.ai/api/v1"
                )
            except ImportError:
                logger.error("OpenAI package not installed. Install with: pip install openai")
                return None
        else:
            logger.warning(f"Provider {self.provider} not implemented yet")
            return None

    def generate_structured_response(self, prompt: str, schema_description: str) -> Dict[str, Any]:
        """
        Generate a structured response from the LLM.

        Args:
            prompt: The input prompt
            schema_description: Description of the expected output schema

        Returns:
            Parsed JSON response
        """
        if not self.client:
            raise ValueError("LLM client not initialized")

        system_prompt = f"""
You are an expert at extracting structured information from enterprise documents.
Please respond with valid JSON that matches this schema:

{schema_description}

Be precise and only extract information that is clearly stated or strongly implied in the text.
"""

        try:
            if self.provider in ["openai", "openrouter"]:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1
                )
                content = response.choices[0].message.content
            elif self.provider == "anthropic":
                response = self.client.messages.create(
                    model=self.model,
                    max_tokens=4000,
                    system=system_prompt,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1
                )
                content = response.content[0].text
            else:
                raise ValueError(f"Provider {self.provider} not supported")

            # Parse JSON response
            return json.loads(content)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {content}")
            return {}
        except Exception as e:
            logger.error(f"LLM API error: {e}")
            return {}


class StandaloneDocumentProcessor:
    """
    Standalone document processor that extracts entities and relationships
    without depending on CocoIndex.
    """

    def __init__(
        self,
        llm_client: LLMClient,
        neo4j_client: Neo4jClient,
        enable_summarization: bool = True,
        focus_relationships: Optional[List[str]] = None,
        focus_entities: Optional[List[str]] = None,
        use_all_constants: bool = True,
        prompt_generator: Optional[PromptGenerator] = None
    ):
        """
        Initialize the standalone processor.

        Args:
            llm_client: LLM client for extraction
            neo4j_client: Neo4j client for storage
            enable_summarization: Whether to generate document summaries
            focus_relationships: List of relationship types to focus on (ignored if use_all_constants=True)
            focus_entities: List of entity types to focus on (ignored if use_all_constants=True)
            use_all_constants: Whether to use all entity and relationship constants
            prompt_generator: Custom prompt generator (optional)
        """
        self.llm_client = llm_client
        self.neo4j_client = neo4j_client
        self.enable_summarization = enable_summarization

        # Initialize prompt generator
        if prompt_generator:
            self.prompt_generator = prompt_generator
        elif use_all_constants:
            self.prompt_generator = create_full_prompt_generator()
        else:
            # Use provided focus types or defaults
            focus_relationships = focus_relationships or [
                "involved_in", "mentions", "works_for", "manages", "reports_to"
            ]
            focus_entities = focus_entities or [
                "Person", "Project", "Company", "Department", "System"
            ]
            self.prompt_generator = PromptGenerator(
                focus_entities=focus_entities,
                focus_relationships=focus_relationships,
                use_all_constants=False
            )

        # Store focus types for backward compatibility
        self.focus_relationships = self.prompt_generator.focus_relationships
        self.focus_entities = self.prompt_generator.focus_entities

    def process_document(self, file_path: str) -> ProcessingMetadata:
        """
        Process a single document and extract entities/relationships.

        Args:
            file_path: Path to the document file

        Returns:
            Processing metadata with results
        """
        logger.info(f"Processing document: {file_path}")

        # Initialize metadata
        metadata = ProcessingMetadata(
            document_id=os.path.basename(file_path),
            document_path=file_path,
            file_size_bytes=0,
            processing_start_time=datetime.now()
        )

        try:
            # Read document
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            metadata.file_size_bytes = len(content.encode('utf-8'))

            # Step 1: Document Summarization (optional)
            summary = None
            if self.enable_summarization:
                summary = self._extract_summary(content)
                metadata.summarization_completed = True
                logger.info(f"Generated summary: {summary.title if summary else 'Failed'}")

            # Step 2: Entity-Relationship Extraction
            relationships = self._extract_relationships(content)
            metadata.entity_extraction_completed = True
            metadata.relationship_extraction_completed = True
            logger.info(f"Extracted {len(relationships)} relationships")

            # Step 3: Store in Neo4j
            if relationships:
                self._store_in_neo4j(relationships, metadata.document_id)
                metadata.graph_storage_completed = True
                logger.info("Stored relationships in Neo4j")

            metadata.processing_end_time = datetime.now()
            logger.info(f"Document processed successfully in {metadata.processing_duration_seconds:.2f}s")

        except Exception as e:
            logger.error(f"Error processing document {file_path}: {e}")
            metadata.errors = [str(e)]
            metadata.processing_end_time = datetime.now()

        return metadata

    def _extract_summary(self, content: str) -> Optional[DocumentSummary]:
        """Extract document summary using LLM with dynamic prompt generation."""
        # Use the prompt generator for consistent summarization
        prompt = self.prompt_generator.generate_summarization_prompt(content)

        schema_description = """
{
    "title": "string - main title or subject of the document",
    "summary": "string - concise summary of key points",
    "document_type": "string - type of document (report, email, proposal, etc.)",
    "key_topics": ["string"] - list of main topics discussed,
    "language": "string - document language",
    "confidence_score": "float - confidence in extraction (0.0-1.0)"
}
"""

        try:
            response = self.llm_client.generate_structured_response(prompt, schema_description)
            if response:
                return DocumentSummary(**response)
        except Exception as e:
            logger.error(f"Failed to extract summary: {e}")

        return None

    def _extract_relationships(self, content: str) -> List[EntityRelationship]:
        """Extract entity relationships using LLM with dynamic prompt generation."""
        # Use the prompt generator for comprehensive relationship extraction
        prompt = self.prompt_generator.generate_relationship_extraction_prompt(content)

        schema_description = """
[
    {
        "subject": "string - the source entity (specific name)",
        "predicate": "string - the relationship type",
        "object": "string - the target entity (specific name)",
        "confidence_score": "float - confidence in this relationship (0.0-1.0)",
        "context": "string - brief context where this was found",
        "source_sentence": "string - the sentence where this relationship was mentioned"
    }
]
"""

        try:
            response = self.llm_client.generate_structured_response(prompt, schema_description)
            if isinstance(response, list):
                relationships = []
                for rel_data in response:
                    try:
                        rel = EntityRelationship(**rel_data)
                        relationships.append(rel)
                    except Exception as e:
                        logger.warning(f"Failed to parse relationship: {rel_data}, error: {e}")
                return relationships
        except Exception as e:
            logger.error(f"Failed to extract relationships: {e}")

        return []

    def _store_in_neo4j(self, relationships: List[EntityRelationship], source_document: str):
        """Store relationships in Neo4j."""
        try:
            results = self.neo4j_client.batch_create_entity_relationships(
                relationships, source_document
            )

            success_count = sum(1 for r in results if r["success"])
            logger.info(f"Successfully stored {success_count}/{len(results)} relationships")

            # Log any failures
            for result in results:
                if not result["success"]:
                    logger.warning(f"Failed to store relationship: {result['error']}")

        except Exception as e:
            logger.error(f"Failed to store relationships in Neo4j: {e}")
            raise

    def process_directory(self, directory_path: str, file_patterns: Optional[List[str]] = None) -> List[ProcessingMetadata]:
        """
        Process all documents in a directory.

        Args:
            directory_path: Path to directory containing documents
            file_patterns: List of file extensions to process (default: ['.md', '.txt'])

        Returns:
            List of processing metadata for each document
        """
        file_patterns = file_patterns or ['.md', '.txt']
        results = []

        logger.info(f"Processing directory: {directory_path}")

        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if any(file.lower().endswith(pattern.lower()) for pattern in file_patterns):
                    file_path = os.path.join(root, file)
                    metadata = self.process_document(file_path)
                    results.append(metadata)

        logger.info(f"Processed {len(results)} documents")
        return results


def create_standalone_processor(
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "password",
    llm_provider: str = "openai",
    llm_model: str = "gpt-4o",
    llm_api_key: Optional[str] = None,
    use_all_constants: bool = True,
    focus_relationships: Optional[List[str]] = None,
    focus_entities: Optional[List[str]] = None
) -> StandaloneDocumentProcessor:
    """
    Create a standalone document processor with default settings.

    Args:
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        llm_provider: LLM provider (openai, anthropic)
        llm_model: LLM model name
        llm_api_key: LLM API key (optional, will use environment variable)
        use_all_constants: Whether to use all entity and relationship constants (default: True)
        focus_relationships: List of relationship types to focus on (ignored if use_all_constants=True)
        focus_entities: List of entity types to focus on (ignored if use_all_constants=True)

    Returns:
        Configured StandaloneDocumentProcessor
    """
    # Create Neo4j client
    neo4j_conn = Neo4jConnection(
        uri=neo4j_uri,
        user=neo4j_user,
        password=neo4j_password
    )
    neo4j_client = Neo4jClient(neo4j_conn)

    # Create LLM client
    llm_client = LLMClient(
        provider=llm_provider,
        model=llm_model,
        api_key=llm_api_key
    )

    # Create processor with comprehensive constant usage by default
    return StandaloneDocumentProcessor(
        llm_client=llm_client,
        neo4j_client=neo4j_client,
        enable_summarization=True,
        use_all_constants=use_all_constants,
        focus_relationships=focus_relationships,
        focus_entities=focus_entities
    )
