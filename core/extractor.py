"""
Entity and relationship extraction component for Enterprise KG

This module handles entity and relationship extraction using LLMs,
following the CocoIndex pattern and using our defined constants.
"""

import cocoindex
from typing import List, Optional, Set
from ..constants.schemas import EntityRelationship, Entity
from ..constants.entities import EntityType, get_all_entity_types
from ..constants.relationships import RelationshipType, get_all_relationship_types, get_common_entity_relationship_patterns


class EntityRelationshipExtractor:
    """
    Handles entity and relationship extraction using LLM extraction.
    
    This class provides entity-relationship extraction capabilities
    using our predefined entity and relationship types.
    """
    
    def __init__(
        self,
        llm_spec: cocoindex.LlmSpec,
        custom_instruction: Optional[str] = None,
        allowed_entity_types: Optional[Set[str]] = None,
        allowed_relationship_types: Optional[Set[str]] = None
    ):
        """
        Initialize the entity-relationship extractor.
        
        Args:
            llm_spec: LLM specification (OpenAI, Anthropic, etc.)
            custom_instruction: Optional custom instruction for extraction
            allowed_entity_types: Set of allowed entity types (default: all)
            allowed_relationship_types: Set of allowed relationship types (default: all)
        """
        self.llm_spec = llm_spec
        self.custom_instruction = custom_instruction or self._get_default_instruction()
        self.allowed_entity_types = allowed_entity_types or get_all_entity_types()
        self.allowed_relationship_types = allowed_relationship_types or get_all_relationship_types()
    
    def _get_default_instruction(self) -> str:
        """Get the default instruction for entity-relationship extraction."""
        entity_types_str = ", ".join(sorted(self.allowed_entity_types))
        relationship_types_str = ", ".join(sorted(self.allowed_relationship_types))
        
        return f"""
Please extract entities and relationships from the enterprise document.

ENTITY TYPES to identify:
{entity_types_str}

RELATIONSHIP TYPES to extract:
{relationship_types_str}

EXTRACTION GUIDELINES:
1. Focus on business-relevant entities and relationships
2. Use exact entity names as they appear in the document
3. Use only the predefined relationship types listed above
4. Ensure subject and object are clear, specific entity names
5. Avoid generic terms - use specific names (e.g., "John Smith" not "the manager")
6. Extract relationships that are explicitly stated or clearly implied
7. Prioritize relationships between people, projects, organizations, and systems

EXAMPLES:
- "John Smith involved_in AI Research Project"
- "Marketing Department reports_to Sarah Johnson"
- "Customer Portal integrates_with CRM System"
- "Q4 Budget funds Digital Transformation Initiative"

Extract all relevant relationships following this pattern.
"""
    
    def create_extraction_function(self) -> cocoindex.functions.ExtractByLlm:
        """
        Create a CocoIndex ExtractByLlm function for entity-relationship extraction.
        
        Returns:
            Configured ExtractByLlm function for extraction
        """
        return cocoindex.functions.ExtractByLlm(
            llm_spec=self.llm_spec,
            output_type=List[EntityRelationship],
            instruction=self.custom_instruction
        )
    
    def create_entity_only_extraction_function(self) -> cocoindex.functions.ExtractByLlm:
        """
        Create a CocoIndex ExtractByLlm function for entity-only extraction.
        
        Returns:
            Configured ExtractByLlm function for entity extraction
        """
        entity_instruction = self._get_entity_only_instruction()
        
        return cocoindex.functions.ExtractByLlm(
            llm_spec=self.llm_spec,
            output_type=List[Entity],
            instruction=entity_instruction
        )
    
    def _get_entity_only_instruction(self) -> str:
        """Get instruction for entity-only extraction."""
        entity_types_str = ", ".join(sorted(self.allowed_entity_types))
        
        return f"""
Please extract all relevant entities from the enterprise document.

ENTITY TYPES to identify:
{entity_types_str}

EXTRACTION GUIDELINES:
1. Identify specific, named entities (not generic references)
2. Use the most specific entity type available
3. Include the exact name as it appears in the document
4. Provide a brief description if the entity's role/purpose is clear
5. Focus on business-relevant entities

EXAMPLES:
- Entity(name="John Smith", entity_type="Person", description="Project Manager")
- Entity(name="CRM System", entity_type="System", description="Customer relationship management platform")
- Entity(name="Q4 Marketing Campaign", entity_type="Campaign", description="Fourth quarter marketing initiative")

Extract all relevant entities following this pattern.
"""
    
    def create_focused_extraction_function(
        self,
        focus_areas: List[str]
    ) -> cocoindex.functions.ExtractByLlm:
        """
        Create an extraction function focused on specific areas.
        
        Args:
            focus_areas: List of focus areas (e.g., ["projects", "people", "systems"])
            
        Returns:
            Configured ExtractByLlm function with focused instruction
        """
        focused_instruction = self._get_focused_instruction(focus_areas)
        
        return cocoindex.functions.ExtractByLlm(
            llm_spec=self.llm_spec,
            output_type=List[EntityRelationship],
            instruction=focused_instruction
        )
    
    def _get_focused_instruction(self, focus_areas: List[str]) -> str:
        """Get instruction for focused extraction."""
        focus_mapping = {
            "projects": {
                "entities": ["Project", "Initiative", "Program", "Campaign"],
                "relationships": ["involved_in", "owns", "responsible_for", "participates_in", "contributes_to"]
            },
            "people": {
                "entities": ["Person", "Employee", "Manager", "Executive", "Consultant"],
                "relationships": ["works_for", "manages", "reports_to", "leads", "collaborates_with"]
            },
            "systems": {
                "entities": ["System", "Application", "Database", "Platform", "Tool"],
                "relationships": ["uses", "integrates_with", "connects_to", "runs_on", "accesses"]
            },
            "organizations": {
                "entities": ["Company", "Department", "Team", "Vendor", "Partner"],
                "relationships": ["part_of", "belongs_to", "partners_with", "contracts_with"]
            },
            "documents": {
                "entities": ["Document", "Report", "Proposal", "Contract", "Policy"],
                "relationships": ["authored_by", "reviewed_by", "approved_by", "references", "implements"]
            }
        }
        
        focused_entities = set()
        focused_relationships = set()
        
        for area in focus_areas:
            if area.lower() in focus_mapping:
                area_config = focus_mapping[area.lower()]
                focused_entities.update(area_config["entities"])
                focused_relationships.update(area_config["relationships"])
        
        # Fallback to all types if no valid focus areas
        if not focused_entities:
            focused_entities = self.allowed_entity_types
        if not focused_relationships:
            focused_relationships = self.allowed_relationship_types
        
        entity_types_str = ", ".join(sorted(focused_entities))
        relationship_types_str = ", ".join(sorted(focused_relationships))
        focus_areas_str = ", ".join(focus_areas)
        
        return f"""
Please extract entities and relationships from the enterprise document with a focus on: {focus_areas_str}.

PRIORITIZED ENTITY TYPES:
{entity_types_str}

PRIORITIZED RELATIONSHIP TYPES:
{relationship_types_str}

EXTRACTION GUIDELINES:
1. Focus specifically on the requested areas: {focus_areas_str}
2. Extract entities and relationships most relevant to these focus areas
3. Use exact entity names as they appear in the document
4. Use only the relationship types listed above
5. Prioritize clear, explicit relationships over implied ones
6. Ensure high relevance to the focus areas

Extract relationships following the subject-predicate-object pattern.
"""


def create_default_extractor(
    api_type: cocoindex.LlmApiType = cocoindex.LlmApiType.OPENAI,
    model: str = "gpt-4o"
) -> EntityRelationshipExtractor:
    """
    Create a default entity-relationship extractor with common settings.
    
    Args:
        api_type: LLM API type (default: OpenAI)
        model: Model name (default: gpt-4o)
        
    Returns:
        Configured EntityRelationshipExtractor instance
    """
    llm_spec = cocoindex.LlmSpec(
        api_type=api_type,
        model=model
    )
    
    return EntityRelationshipExtractor(llm_spec=llm_spec)


def create_basic_extractor(
    api_type: cocoindex.LlmApiType = cocoindex.LlmApiType.OPENAI,
    model: str = "gpt-4o"
) -> EntityRelationshipExtractor:
    """
    Create a basic extractor with only the fundamental relationship types.
    
    This matches your initial requirements: Person-involved_in-Project, Project-mentions-Person
    
    Args:
        api_type: LLM API type (default: OpenAI)
        model: Model name (default: gpt-4o)
        
    Returns:
        Configured EntityRelationshipExtractor with basic types only
    """
    llm_spec = cocoindex.LlmSpec(
        api_type=api_type,
        model=model
    )
    
    # Basic entity types
    basic_entity_types = {
        EntityType.PERSON.value,
        EntityType.PROJECT.value,
        EntityType.EMPLOYEE.value,
        EntityType.MANAGER.value,
        EntityType.TEAM.value,
        EntityType.COMPANY.value,
    }
    
    # Basic relationship types (your initial requirements)
    basic_relationship_types = {
        RelationshipType.INVOLVED_IN.value,
        RelationshipType.MENTIONS.value,
        RelationshipType.WORKS_FOR.value,
        RelationshipType.MANAGES.value,
        RelationshipType.MEMBER_OF.value,
    }
    
    return EntityRelationshipExtractor(
        llm_spec=llm_spec,
        allowed_entity_types=basic_entity_types,
        allowed_relationship_types=basic_relationship_types
    )
