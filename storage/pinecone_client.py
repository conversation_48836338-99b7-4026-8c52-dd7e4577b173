"""
Pinecone vector database client for Enterprise KG

This module provides integration with Pinecone for storing and retrieving
document embeddings and semantic search capabilities.

NOTE: Currently commented out for Neo4j-only testing.
"""

import cocoindex
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from ..constants.schemas import VectorDocument


@dataclass
class PineconeConnection:
    """Connection specification for Pinecone."""

    api_key: str
    environment: str
    index_name: str
    dimension: int = 1536  # Default for OpenAI embeddings


class PineconeClient:
    """
    Client for interacting with Pinecone vector database.

    This class provides methods for storing and retrieving document embeddings
    following the CocoIndex storage pattern.
    """

    def __init__(self, connection: PineconeConnection):
        """
        Initialize the Pinecone client.

        Args:
            connection: Pinecone connection configuration
        """
        self.connection = connection
        self._client = None
        self._index = None

    def _get_client(self):
        """Get or create Pinecone client."""
        if self._client is None:
            try:
                import pinecone
                pinecone.init(
                    api_key=self.connection.api_key,
                    environment=self.connection.environment
                )
                self._client = pinecone
            except ImportError:
                raise ImportError(
                    "Pinecone client not installed. Install with: pip install pinecone-client"
                )
        return self._client

    def _get_index(self):
        """Get or create Pinecone index."""
        if self._index is None:
            client = self._get_client()

            # Check if index exists, create if not
            if self.connection.index_name not in client.list_indexes():
                client.create_index(
                    name=self.connection.index_name,
                    dimension=self.connection.dimension,
                    metric="cosine"
                )

            self._index = client.Index(self.connection.index_name)
        return self._index

    def upsert_documents(self, documents: List[VectorDocument]) -> Dict[str, Any]:
        """
        Upsert documents into Pinecone index.

        Args:
            documents: List of vector documents to upsert

        Returns:
            Upsert response from Pinecone
        """
        index = self._get_index()

        # Convert documents to Pinecone format
        vectors = []
        for doc in documents:
            vector_data = {
                "id": doc.document_id,
                "values": doc.embedding,
                "metadata": {
                    "text": doc.text[:1000],  # Truncate text for metadata
                    **doc.metadata
                }
            }
            vectors.append(vector_data)

        # Upsert in batches if needed
        batch_size = 100
        responses = []

        for i in range(0, len(vectors), batch_size):
            batch = vectors[i:i + batch_size]
            response = index.upsert(vectors=batch)
            responses.append(response)

        return {"batch_responses": responses, "total_upserted": len(vectors)}

    def query_similar_documents(
        self,
        query_embedding: List[float],
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Query for similar documents using vector similarity.

        Args:
            query_embedding: Query vector embedding
            top_k: Number of top results to return
            filter_metadata: Optional metadata filters

        Returns:
            List of similar documents with scores
        """
        index = self._get_index()

        response = index.query(
            vector=query_embedding,
            top_k=top_k,
            filter=filter_metadata,
            include_metadata=True,
            include_values=False
        )

        return response.matches

    def delete_documents(self, document_ids: List[str]) -> Dict[str, Any]:
        """
        Delete documents from Pinecone index.

        Args:
            document_ids: List of document IDs to delete

        Returns:
            Delete response from Pinecone
        """
        index = self._get_index()
        return index.delete(ids=document_ids)

    def get_index_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the Pinecone index.

        Returns:
            Index statistics
        """
        index = self._get_index()
        return index.describe_index_stats()


def create_pinecone_storage_spec(
    connection: PineconeConnection,
    embedding_field: str = "embedding",
    metadata_fields: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Create a storage specification for Pinecone integration with CocoIndex.

    This function creates a configuration that can be used with CocoIndex
    for storing embeddings in Pinecone.

    Args:
        connection: Pinecone connection configuration
        embedding_field: Name of the field containing embeddings
        metadata_fields: List of fields to include as metadata

    Returns:
        Storage specification dictionary
    """
    metadata_fields = metadata_fields or ["filename", "document_type", "title"]

    return {
        "type": "pinecone",
        "connection": connection,
        "embedding_field": embedding_field,
        "metadata_fields": metadata_fields,
        "index_name": connection.index_name
    }


def create_embedding_function(
    model: str = "sentence-transformers/all-MiniLM-L6-v2"
) -> cocoindex.functions.SentenceTransformerEmbed:
    """
    Create an embedding function for use with CocoIndex.

    Args:
        model: Name of the SentenceTransformer model to use

    Returns:
        Configured SentenceTransformerEmbed function
    """
    return cocoindex.functions.SentenceTransformerEmbed(model=model)


def create_openai_embedding_function(
    model: str = "text-embedding-ada-002"
) -> cocoindex.functions.ExtractByLlm:
    """
    Create an OpenAI embedding function for use with CocoIndex.

    Note: This is a placeholder - CocoIndex may need specific OpenAI embedding support.

    Args:
        model: OpenAI embedding model name

    Returns:
        Configured embedding function
    """
    # This would need to be implemented based on CocoIndex's OpenAI embedding support
    # For now, fall back to SentenceTransformer
    return create_embedding_function()


# Example usage functions
def create_default_pinecone_client(
    api_key: str,
    environment: str,
    index_name: str = "enterprise-kg",
    dimension: int = 1536
) -> PineconeClient:
    """
    Create a default Pinecone client with standard settings.

    Args:
        api_key: Pinecone API key
        environment: Pinecone environment
        index_name: Name of the Pinecone index
        dimension: Vector dimension

    Returns:
        Configured PineconeClient instance
    """
    connection = PineconeConnection(
        api_key=api_key,
        environment=environment,
        index_name=index_name,
        dimension=dimension
    )

    return PineconeClient(connection)
