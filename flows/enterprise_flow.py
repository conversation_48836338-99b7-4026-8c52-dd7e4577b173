"""
Main enterprise flow for Enterprise KG

This module provides the main flow definition and factory functions
for creating enterprise knowledge graph processing flows.
"""

import cocoindex
from typing import Optional, List, Dict, Any
from ..core.pipeline import ProcessingPipeline, create_default_pipeline
from ..core.summarizer import create_default_summarizer, create_basic_extractor
from ..core.extractor import create_default_extractor
from ..storage.pinecone_client import PineconeConnection, PineconeClient
from ..storage.neo4j_client import Neo4jConnection, Neo4jClient
from ..constants.schemas import DocumentSummary, EntityRelationship


class EnterpriseKGFlow:
    """
    Main enterprise knowledge graph flow class.
    
    This class provides a high-level interface for creating and running
    enterprise knowledge graph processing flows.
    """
    
    def __init__(
        self,
        pipeline: ProcessingPipeline,
        flow_name: str = "EnterpriseKG",
        source_path: str = "documents"
    ):
        """
        Initialize the enterprise KG flow.
        
        Args:
            pipeline: Processing pipeline instance
            flow_name: Name of the flow
            source_path: Path to source documents
        """
        self.pipeline = pipeline
        self.flow_name = flow_name
        self.source_path = source_path
        self._flow_function = None
    
    def create_flow(
        self,
        included_patterns: Optional[List[str]] = None,
        excluded_patterns: Optional[List[str]] = None
    ):
        """
        Create the CocoIndex flow function.
        
        Args:
            included_patterns: File patterns to include
            excluded_patterns: File patterns to exclude
        """
        included_patterns = included_patterns or ["*.md", "*.txt", "*.pdf", "*.docx"]
        excluded_patterns = excluded_patterns or ["**/.*", "**/__pycache__"]
        
        self._flow_function = self.pipeline.create_cocoindex_flow(
            flow_name=self.flow_name,
            source_path=self.source_path,
            included_patterns=included_patterns,
            excluded_patterns=excluded_patterns
        )
        
        return self._flow_function
    
    def run(self, **kwargs):
        """
        Run the enterprise KG flow.
        
        Args:
            **kwargs: Additional arguments for flow execution
        """
        if self._flow_function is None:
            self.create_flow()
        
        # This would integrate with CocoIndex's execution system
        # For now, return the flow function
        return self._flow_function


def create_enterprise_flow(
    source_path: str = "documents",
    flow_name: str = "EnterpriseKG",
    llm_config: Optional[Dict[str, Any]] = None,
    pinecone_config: Optional[Dict[str, str]] = None,
    neo4j_config: Optional[Dict[str, str]] = None,
    use_basic_extraction: bool = True
) -> EnterpriseKGFlow:
    """
    Create an enterprise knowledge graph flow with default settings.
    
    Args:
        source_path: Path to source documents
        flow_name: Name of the flow
        llm_config: LLM configuration (api_type, model)
        pinecone_config: Pinecone configuration
        neo4j_config: Neo4j configuration
        use_basic_extraction: Whether to use basic extraction (your initial requirements)
        
    Returns:
        Configured EnterpriseKGFlow instance
    """
    # Default LLM configuration
    llm_config = llm_config or {
        "api_type": cocoindex.LlmApiType.OPENAI,
        "model": "gpt-4o"
    }
    
    # Create pipeline
    if use_basic_extraction:
        # Use basic extraction with your initial requirements
        pipeline = create_basic_pipeline(
            llm_config=llm_config,
            pinecone_config=pinecone_config,
            neo4j_config=neo4j_config
        )
    else:
        # Use full extraction with all entity/relationship types
        pipeline = create_default_pipeline(
            api_type=llm_config["api_type"],
            model=llm_config["model"],
            pinecone_config=pinecone_config,
            neo4j_config=neo4j_config
        )
    
    return EnterpriseKGFlow(
        pipeline=pipeline,
        flow_name=flow_name,
        source_path=source_path
    )


def create_basic_pipeline(
    llm_config: Dict[str, Any],
    pinecone_config: Optional[Dict[str, str]] = None,
    neo4j_config: Optional[Dict[str, str]] = None
) -> ProcessingPipeline:
    """
    Create a basic pipeline with your initial requirements.
    
    This creates a pipeline focused on:
    - Person involved_in Project
    - Project mentions Person
    
    Args:
        llm_config: LLM configuration
        pinecone_config: Optional Pinecone configuration
        neo4j_config: Optional Neo4j configuration
        
    Returns:
        Configured ProcessingPipeline with basic extraction
    """
    # Create LLM spec
    llm_spec = cocoindex.LlmSpec(
        api_type=llm_config["api_type"],
        model=llm_config["model"]
    )
    
    # Create components with basic extraction
    summarizer = create_default_summarizer(
        api_type=llm_config["api_type"],
        model=llm_config["model"]
    )
    
    extractor = create_basic_extractor(
        api_type=llm_config["api_type"],
        model=llm_config["model"]
    )
    
    # Create storage clients if configurations provided
    pinecone_client = None
    if pinecone_config:
        pinecone_conn = PineconeConnection(**pinecone_config)
        pinecone_client = PineconeClient(pinecone_conn)
    
    neo4j_client = None
    if neo4j_config:
        neo4j_conn = Neo4jConnection(**neo4j_config)
        neo4j_client = Neo4jClient(neo4j_conn)
    
    return ProcessingPipeline(
        summarizer=summarizer,
        extractor=extractor,
        pinecone_client=pinecone_client,
        neo4j_client=neo4j_client
    )


@cocoindex.flow_def(name="BasicEnterpriseKG")
def basic_enterprise_kg_flow(
    flow_builder: cocoindex.FlowBuilder, 
    data_scope: cocoindex.DataScope
):
    """
    Basic enterprise KG flow with your initial requirements.
    
    This flow extracts:
    - Person involved_in Project
    - Project mentions Person
    """
    # Configure Neo4j connection
    neo4j_conn_spec = cocoindex.add_auth_entry(
        "EnterpriseKG_Neo4j",
        cocoindex.storages.Neo4jConnection(
            uri="bolt://localhost:7687",
            user="neo4j",
            password="cocoindex",
        ),
    )
    
    # Configure data source
    data_scope["documents"] = flow_builder.add_source(
        cocoindex.sources.LocalFile(
            path="documents",
            included_patterns=["*.md", "*.txt", "*.pdf"]
        )
    )
    
    # Collectors
    document_summaries = data_scope.add_collector()
    entity_relationships = data_scope.add_collector()
    
    with data_scope["documents"].row() as doc:
        # Stage 1: Document Summarization
        doc["summary"] = doc["content"].transform(
            cocoindex.functions.ExtractByLlm(
                llm_spec=cocoindex.LlmSpec(
                    api_type=cocoindex.LlmApiType.OPENAI,
                    model="gpt-4o",
                ),
                output_type=DocumentSummary,
                instruction="Please summarize the document focusing on people, projects, and their relationships."
            )
        )
        
        # Collect summaries
        document_summaries.collect(
            filename=doc["filename"],
            title=doc["summary"]["title"],
            summary=doc["summary"]["summary"],
            document_type=doc["summary"]["document_type"]
        )
        
        # Stage 2: Entity and Relationship Extraction
        doc["relationships"] = doc["content"].transform(
            cocoindex.functions.ExtractByLlm(
                llm_spec=cocoindex.LlmSpec(
                    api_type=cocoindex.LlmApiType.OPENAI,
                    model="gpt-4o",
                ),
                output_type=List[EntityRelationship],
                instruction=(
                    "Extract relationships from the document focusing on:\n"
                    "1. Person involved_in Project\n"
                    "2. Project mentions Person\n"
                    "3. Person works_for Company\n"
                    "4. Person manages Team\n\n"
                    "Use specific names and the exact relationship types listed above."
                )
            )
        )
        
        # Collect relationships
        with doc["relationships"].row() as relationship:
            entity_relationships.collect(
                id=cocoindex.GeneratedField.UUID,
                subject=relationship["subject"],
                predicate=relationship["predicate"],
                object=relationship["object"],
                source_document=doc["filename"]
            )
    
    # Export to storage
    document_summaries.export(
        "document_summaries",
        cocoindex.storages.Postgres(table_name="document_summaries"),
        primary_key_fields=["filename"]
    )
    
    # Declare Entity nodes for Neo4j
    flow_builder.declare(
        cocoindex.storages.Neo4jDeclaration(
            connection=neo4j_conn_spec,
            nodes_label="Entity",
            primary_key_fields=["name"]
        )
    )
    
    # Export relationships to Neo4j
    entity_relationships.export(
        "entity_relationships",
        cocoindex.storages.Neo4j(
            connection=neo4j_conn_spec,
            mapping=cocoindex.storages.Relationships(
                rel_type="RELATIONSHIP",
                source=cocoindex.storages.NodeFromFields(
                    label="Entity",
                    fields=[
                        cocoindex.storages.TargetFieldMapping(
                            source="subject", target="name"
                        )
                    ]
                ),
                target=cocoindex.storages.NodeFromFields(
                    label="Entity",
                    fields=[
                        cocoindex.storages.TargetFieldMapping(
                            source="object", target="name"
                        )
                    ]
                )
            )
        ),
        primary_key_fields=["id"]
    )
