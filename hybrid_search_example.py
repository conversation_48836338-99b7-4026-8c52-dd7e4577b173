"""
Complete Hybrid Search Example

This example shows how to integrate both approaches with your existing Pinecone setup.
"""

import json
from typing import Dict, List, Any
from knowledge_enrichment import DynamicQueryBuilder
from entity_discovery import HybridEntitySearch


class EnterpriseSearchEngine:
    """Complete enterprise search engine combining both approaches."""
    
    def __init__(self, pinecone_client, neo4j_client, llm_client):
        self.pinecone = pinecone_client
        self.neo4j = neo4j_client
        self.llm = llm_client
        
        # Initialize both approaches
        self.template_builder = DynamicQueryBuilder(neo4j_client)
        self.entity_discovery = HybridEntitySearch(pinecone_client, neo4j_client, llm_client)
    
    def search(self, query: str, org_id: str = None, method: str = "auto") -> Dict[str, Any]:
        """
        Perform enterprise search using the specified method.
        
        Args:
            query: Search query
            org_id: Organization ID for filtering
            method: "template", "discovery", or "auto"
        """
        if method == "template":
            return self._template_based_search(query, org_id)
        elif method == "discovery":
            return self._discovery_based_search(query, org_id)
        else:  # auto
            return self._auto_search(query, org_id)
    
    def _template_based_search(self, query: str, org_id: str = None) -> Dict[str, Any]:
        """Search using knowledge enrichment templates."""
        
        # Step 1: Pinecone semantic search
        pinecone_results = self._search_pinecone(query, org_id)
        
        # Step 2: Template-based Neo4j query
        cypher_query, parameters = self.template_builder.build_dynamic_query(query)
        structured_results = self._execute_neo4j_query(cypher_query, parameters)
        
        return {
            "method": "template_based",
            "query": query,
            "semantic_context": self._extract_chunk_texts(pinecone_results),
            "structured_relationships": structured_results,
            "generated_cypher": cypher_query,
            "parameters": parameters,
            "source_files": self._extract_file_ids(pinecone_results)
        }
    
    def _discovery_based_search(self, query: str, org_id: str = None) -> Dict[str, Any]:
        """Search using entity discovery from Pinecone."""
        return self.entity_discovery.search(query, org_id)
    
    def _auto_search(self, query: str, org_id: str = None) -> Dict[str, Any]:
        """Automatically choose the best method based on query characteristics."""
        
        # Use discovery method if query is complex or mentions specific entities
        if self._should_use_discovery(query):
            result = self._discovery_based_search(query, org_id)
            result["method"] = "auto_discovery"
            return result
        else:
            result = self._template_based_search(query, org_id)
            result["method"] = "auto_template"
            return result
    
    def _should_use_discovery(self, query: str) -> bool:
        """Determine if discovery method should be used."""
        # Use discovery for queries with specific entity names
        import re
        has_proper_nouns = bool(re.search(r'\b[A-Z][a-z]+ [A-Z][a-z]+\b', query))
        has_specific_names = any(word in query for word in ["Alpha", "Beta", "Johnson", "Smith", "Chen"])
        
        return has_proper_nouns or has_specific_names
    
    def _search_pinecone(self, query: str, org_id: str = None) -> Dict:
        """Search Pinecone with your existing setup."""
        # This uses your existing Pinecone integration
        query_embedding = self._create_embedding(query)
        
        filter_metadata = {}
        if org_id:
            filter_metadata["org_id"] = org_id
        
        return self.pinecone.query(
            vector=query_embedding,
            top_k=10,
            filter=filter_metadata,
            include_metadata=True
        )
    
    def _execute_neo4j_query(self, cypher_query: str, parameters: Dict) -> List[Dict]:
        """Execute Neo4j query."""
        try:
            with self.neo4j._get_driver().session() as session:
                results = session.run(cypher_query, parameters)
                return [dict(record) for record in results]
        except Exception as e:
            print(f"Neo4j query failed: {e}")
            return []
    
    def _create_embedding(self, text: str) -> List[float]:
        """Create embedding using your existing method."""
        # Use your existing embedding creation logic
        pass
    
    def _extract_chunk_texts(self, pinecone_results: Dict) -> List[str]:
        """Extract chunk texts from Pinecone results."""
        return [
            match["metadata"]["chunk_text"] 
            for match in pinecone_results.get("matches", [])
        ]
    
    def _extract_file_ids(self, pinecone_results: Dict) -> List[str]:
        """Extract file IDs from Pinecone results."""
        return list(set([
            match["metadata"]["file_id"] 
            for match in pinecone_results.get("matches", [])
        ]))
    
    def generate_comprehensive_answer(self, search_result: Dict) -> str:
        """Generate a comprehensive answer using LLM."""
        context = f"""
        Query: {search_result['query']}
        
        Semantic Context from Documents:
        {chr(10).join(search_result['semantic_context'])}
        
        Structured Knowledge Graph Data:
        {json.dumps(search_result['structured_relationships'], indent=2)}
        
        Source Files: {', '.join(search_result['source_files'])}
        """
        
        prompt = f"""
        Based on the enterprise data below, provide a comprehensive answer to the query.
        Combine information from both the document context and structured relationships.
        
        {context}
        
        Answer:
        """
        
        return self.llm.generate_response(prompt)


# Example usage with your existing setup
def example_usage():
    """Example of how to use the hybrid search engine."""
    
    # Initialize with your existing clients
    search_engine = EnterpriseSearchEngine(
        pinecone_client=your_existing_pinecone_client,
        neo4j_client=neo4j_client,
        llm_client=llm_client
    )
    
    # Example queries
    queries = [
        "Who is working on Project Alpha?",
        "What systems does Mike Chen work with?", 
        "Show me all projects in the Engineering Department",
        "How is the CRM System connected to other systems?"
    ]
    
    for query in queries:
        print(f"\n{'='*50}")
        print(f"Query: {query}")
        print('='*50)
        
        # Try different methods
        for method in ["template", "discovery", "auto"]:
            print(f"\n--- {method.upper()} METHOD ---")
            
            result = search_engine.search(query, org_id="tech_corp_001", method=method)
            
            print(f"Generated Cypher: {result.get('generated_cypher', 'N/A')}")
            print(f"Structured Results: {len(result.get('structured_relationships', []))} relationships found")
            print(f"Semantic Context: {len(result.get('semantic_context', []))} chunks found")
            print(f"Source Files: {result.get('source_files', [])}")
            
            # Generate comprehensive answer
            answer = search_engine.generate_comprehensive_answer(result)
            print(f"Answer: {answer}")


# Integration with your main project
class YourMainProjectIntegration:
    """Example of how to integrate with your existing system."""
    
    def __init__(self):
        # Your existing Pinecone setup
        self.existing_pinecone_client = self.setup_existing_pinecone()
        
        # Add Enterprise KG components
        self.neo4j_client = self.setup_neo4j()
        self.llm_client = self.setup_llm()
        
        # Create hybrid search engine
        self.search_engine = EnterpriseSearchEngine(
            self.existing_pinecone_client,
            self.neo4j_client, 
            self.llm_client
        )
    
    def enhanced_search(self, query: str, user_org_id: str) -> Dict:
        """Enhanced search that combines your existing flow with KG."""
        
        # Your existing search logic
        existing_results = self.your_existing_search_method(query, user_org_id)
        
        # Add knowledge graph enhancement
        kg_results = self.search_engine.search(query, org_id=user_org_id, method="auto")
        
        # Combine results
        return {
            "semantic_search": existing_results,  # Your existing Pinecone results
            "knowledge_graph": kg_results,        # New KG relationships
            "enhanced_answer": self.search_engine.generate_comprehensive_answer(kg_results)
        }
    
    def setup_existing_pinecone(self):
        """Your existing Pinecone setup."""
        pass
    
    def setup_neo4j(self):
        """Setup Neo4j for knowledge graph."""
        pass
    
    def setup_llm(self):
        """Setup LLM client."""
        pass
    
    def your_existing_search_method(self, query: str, org_id: str):
        """Your existing search implementation."""
        pass


if __name__ == "__main__":
    example_usage()
