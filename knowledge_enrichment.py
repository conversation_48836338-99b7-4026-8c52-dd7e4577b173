"""
Knowledge Enrichment Module

This module contains query templates and relationship mappings for dynamic
Cypher query construction based on detected entities and query intent.
"""

from typing import Dict, List, Tuple, Optional
from enum import Enum


class QueryIntent(Enum):
    """Different types of query intents."""
    WHO_WORKS_ON = "who_works_on"
    WHAT_PROJECTS = "what_projects" 
    WHO_MANAGES = "who_manages"
    WHAT_SYSTEMS = "what_systems"
    HOW_CONNECTED = "how_connected"
    FIND_RELATIONSHIPS = "find_relationships"
    ORGANIZATIONAL_STRUCTURE = "org_structure"
    SYSTEM_DEPENDENCIES = "system_deps"


class RelationshipPattern:
    """Defines a relationship pattern for query construction."""
    
    def __init__(self, 
                 source_types: List[str], 
                 relationship_types: List[str], 
                 target_types: List[str],
                 description: str):
        self.source_types = source_types
        self.relationship_types = relationship_types
        self.target_types = target_types
        self.description = description


# Define all possible relationship patterns
RELATIONSHIP_PATTERNS = {
    "person_to_project": RelationshipPattern(
        source_types=["Person", "Employee", "Manager", "Executive"],
        relationship_types=["INVOLVED_IN", "MANAGES", "OWNS", "RESPONSIBLE_FOR"],
        target_types=["Project", "Initiative", "Program"],
        description="People working on projects"
    ),
    
    "person_to_organization": RelationshipPattern(
        source_types=["Person", "Employee", "Manager", "Executive"],
        relationship_types=["WORKS_FOR", "MEMBER_OF", "LEADS", "MANAGES"],
        target_types=["Company", "Department", "Team"],
        description="People in organizations"
    ),
    
    "project_to_system": RelationshipPattern(
        source_types=["Project", "Initiative", "Program"],
        relationship_types=["MENTIONS", "USES", "IMPLEMENTS", "INTEGRATES_WITH"],
        target_types=["System", "Application", "Platform", "Database"],
        description="Projects using systems"
    ),
    
    "system_to_system": RelationshipPattern(
        source_types=["System", "Application", "Platform", "Database"],
        relationship_types=["INTEGRATES_WITH", "CONNECTS_TO", "DEPENDS_ON"],
        target_types=["System", "Application", "Platform", "Database"],
        description="System integrations"
    ),
    
    "hierarchical": RelationshipPattern(
        source_types=["Person", "Department", "Team"],
        relationship_types=["REPORTS_TO", "PART_OF", "BELONGS_TO"],
        target_types=["Person", "Department", "Company"],
        description="Organizational hierarchy"
    ),
    
    "document_authorship": RelationshipPattern(
        source_types=["Document", "Report", "Proposal"],
        relationship_types=["AUTHORED_BY", "REVIEWED_BY", "APPROVED_BY"],
        target_types=["Person", "Employee", "Manager"],
        description="Document authorship"
    )
}


# Query templates for different intents
QUERY_TEMPLATES = {
    QueryIntent.WHO_WORKS_ON: {
        "template": """
        MATCH (person:Entity)-[r:{relationship_types}]->(target:Entity)
        WHERE target.name CONTAINS $entity_name 
        AND person.type IN $person_types
        AND target.type IN $target_types
        RETURN person.name as person, person.type as person_type, 
               type(r) as relationship, target.name as target, target.type as target_type
        """,
        "patterns": ["person_to_project", "person_to_organization"]
    },
    
    QueryIntent.WHAT_PROJECTS: {
        "template": """
        MATCH (source:Entity)-[r:{relationship_types}]->(project:Entity)
        WHERE source.name CONTAINS $entity_name
        AND source.type IN $source_types
        AND project.type IN $project_types
        RETURN source.name as source, source.type as source_type,
               type(r) as relationship, project.name as project, project.type as project_type
        """,
        "patterns": ["person_to_project"]
    },
    
    QueryIntent.WHAT_SYSTEMS: {
        "template": """
        MATCH (source:Entity)-[r1:{relationship_types1}]->(intermediate:Entity)
        MATCH (intermediate)-[r2:{relationship_types2}]->(system:Entity)
        WHERE source.name CONTAINS $entity_name
        AND system.type IN $system_types
        RETURN source.name, intermediate.name, system.name, type(r1), type(r2)
        
        UNION
        
        MATCH (source:Entity)-[r:{relationship_types}]->(system:Entity)
        WHERE source.name CONTAINS $entity_name
        AND system.type IN $system_types
        RETURN source.name, null as intermediate, system.name, type(r), null as r2
        """,
        "patterns": ["person_to_project", "project_to_system"]
    },
    
    QueryIntent.HOW_CONNECTED: {
        "template": """
        MATCH path = shortestPath((entity1:Entity)-[*1..4]-(entity2:Entity))
        WHERE entity1.name CONTAINS $entity1_name 
        AND entity2.name CONTAINS $entity2_name
        RETURN path, length(path) as distance
        ORDER BY distance
        LIMIT 5
        """,
        "patterns": ["any"]
    },
    
    QueryIntent.ORGANIZATIONAL_STRUCTURE: {
        "template": """
        MATCH (person:Entity)-[r:{relationship_types}]->(org:Entity)
        WHERE (person.name CONTAINS $entity_name OR org.name CONTAINS $entity_name)
        AND person.type IN $person_types
        AND org.type IN $org_types
        RETURN person.name, person.type, type(r), org.name, org.type
        """,
        "patterns": ["person_to_organization", "hierarchical"]
    },
    
    QueryIntent.SYSTEM_DEPENDENCIES: {
        "template": """
        MATCH (system1:Entity)-[r:{relationship_types}]->(system2:Entity)
        WHERE (system1.name CONTAINS $entity_name OR system2.name CONTAINS $entity_name)
        AND system1.type IN $system_types
        AND system2.type IN $system_types
        RETURN system1.name, system1.type, type(r), system2.name, system2.type
        """,
        "patterns": ["system_to_system"]
    }
}


class DynamicQueryBuilder:
    """Builds Cypher queries dynamically based on detected entities and intent."""
    
    def __init__(self, neo4j_client):
        self.neo4j_client = neo4j_client
        self.entity_cache = {}  # Cache for entity type lookups
    
    def detect_query_intent(self, query: str) -> QueryIntent:
        """Detect the intent of the query."""
        query_lower = query.lower()
        
        if any(phrase in query_lower for phrase in ["who works", "who is working", "who involved"]):
            return QueryIntent.WHO_WORKS_ON
        elif any(phrase in query_lower for phrase in ["what projects", "which projects"]):
            return QueryIntent.WHAT_PROJECTS
        elif any(phrase in query_lower for phrase in ["who manages", "who leads"]):
            return QueryIntent.WHO_MANAGES
        elif any(phrase in query_lower for phrase in ["what systems", "which systems", "systems does"]):
            return QueryIntent.WHAT_SYSTEMS
        elif any(phrase in query_lower for phrase in ["how connected", "relationship between", "connection"]):
            return QueryIntent.HOW_CONNECTED
        elif any(phrase in query_lower for phrase in ["organization", "department", "team structure"]):
            return QueryIntent.ORGANIZATIONAL_STRUCTURE
        elif any(phrase in query_lower for phrase in ["system integration", "dependencies", "connects to"]):
            return QueryIntent.SYSTEM_DEPENDENCIES
        else:
            return QueryIntent.FIND_RELATIONSHIPS
    
    def extract_entities_from_query(self, query: str) -> List[str]:
        """Extract potential entity names from query."""
        # Simple extraction - in practice, you'd use NER or LLM
        import re
        
        # Look for capitalized words/phrases that might be entities
        potential_entities = re.findall(r'\b[A-Z][a-zA-Z\s]*(?:[A-Z][a-zA-Z]*)*\b', query)
        
        # Filter out common words
        common_words = {"What", "Who", "How", "Where", "When", "Which", "The", "And", "Or", "In", "On", "At"}
        entities = [entity.strip() for entity in potential_entities if entity.strip() not in common_words]
        
        return entities
    
    def get_entity_types(self, entity_names: List[str]) -> Dict[str, List[str]]:
        """Get entity types from Neo4j for given entity names."""
        entity_types = {}
        
        for entity_name in entity_names:
            if entity_name in self.entity_cache:
                entity_types[entity_name] = self.entity_cache[entity_name]
                continue
            
            # Query Neo4j to find entity types
            query = """
            MATCH (e:Entity)
            WHERE e.name CONTAINS $entity_name
            RETURN DISTINCT e.type as entity_type, e.name as exact_name
            """
            
            results = self.neo4j_client._get_driver().session().run(query, entity_name=entity_name)
            types = [record["entity_type"] for record in results]
            
            entity_types[entity_name] = types
            self.entity_cache[entity_name] = types
        
        return entity_types
    
    def build_dynamic_query(self, query: str) -> Tuple[str, Dict]:
        """Build a dynamic Cypher query based on the input query."""
        # Step 1: Detect intent
        intent = self.detect_query_intent(query)
        
        # Step 2: Extract entities
        entities = self.extract_entities_from_query(query)
        
        # Step 3: Get entity types from Neo4j
        entity_types = self.get_entity_types(entities)
        
        # Step 4: Get query template
        template_info = QUERY_TEMPLATES.get(intent)
        if not template_info:
            return self._build_generic_query(entities, entity_types)
        
        # Step 5: Build query based on patterns
        cypher_query, parameters = self._build_query_from_template(
            template_info, entities, entity_types
        )
        
        return cypher_query, parameters
    
    def _build_query_from_template(self, template_info: Dict, entities: List[str], entity_types: Dict) -> Tuple[str, Dict]:
        """Build query from template and detected entities."""
        template = template_info["template"]
        patterns = template_info["patterns"]
        
        # Collect all relevant relationship types and entity types
        all_relationship_types = set()
        all_source_types = set()
        all_target_types = set()
        
        for pattern_name in patterns:
            if pattern_name == "any":
                continue
                
            pattern = RELATIONSHIP_PATTERNS[pattern_name]
            all_relationship_types.update(pattern.relationship_types)
            all_source_types.update(pattern.source_types)
            all_target_types.update(pattern.target_types)
        
        # Build parameters
        parameters = {}
        if entities:
            parameters["entity_name"] = entities[0]  # Use first entity
            if len(entities) > 1:
                parameters["entity1_name"] = entities[0]
                parameters["entity2_name"] = entities[1]
        
        # Add type filters
        parameters["person_types"] = ["Person", "Employee", "Manager", "Executive"]
        parameters["project_types"] = ["Project", "Initiative", "Program"]
        parameters["system_types"] = ["System", "Application", "Platform", "Database"]
        parameters["org_types"] = ["Company", "Department", "Team"]
        parameters["source_types"] = list(all_source_types)
        parameters["target_types"] = list(all_target_types)
        
        # Format relationship types in template
        relationship_types_str = "|".join(all_relationship_types)
        formatted_template = template.format(
            relationship_types=relationship_types_str,
            relationship_types1="|".join(["INVOLVED_IN", "WORKS_FOR", "MANAGES"]),
            relationship_types2="|".join(["MENTIONS", "USES", "INTEGRATES_WITH"])
        )
        
        return formatted_template, parameters
    
    def _build_generic_query(self, entities: List[str], entity_types: Dict) -> Tuple[str, Dict]:
        """Build a generic query when intent is unclear."""
        if not entities:
            # Return all relationships
            query = """
            MATCH (source:Entity)-[r]->(target:Entity)
            RETURN source.name, source.type, type(r), target.name, target.type
            LIMIT 50
            """
            return query, {}
        
        # Find all relationships involving the entities
        query = """
        MATCH (entity:Entity)-[r]-(connected:Entity)
        WHERE entity.name CONTAINS $entity_name
        RETURN entity.name, entity.type, type(r), connected.name, connected.type
        LIMIT 20
        """
        
        return query, {"entity_name": entities[0]}
    
    def execute_dynamic_query(self, query: str) -> List[Dict]:
        """Execute a dynamic query and return results."""
        cypher_query, parameters = self.build_dynamic_query(query)
        
        print(f"Generated Cypher: {cypher_query}")
        print(f"Parameters: {parameters}")
        
        with self.neo4j_client._get_driver().session() as session:
            results = session.run(cypher_query, parameters)
            return [dict(record) for record in results]


# Usage example
def create_knowledge_enriched_search(neo4j_client, pinecone_client, llm_client):
    """Create a search system with knowledge enrichment."""
    
    class KnowledgeEnrichedSearch:
        def __init__(self):
            self.query_builder = DynamicQueryBuilder(neo4j_client)
            self.pinecone = pinecone_client
            self.llm = llm_client
        
        def search(self, query: str, org_id: str = None) -> Dict:
            # Step 1: Pinecone semantic search
            semantic_results = self._pinecone_search(query, org_id)
            
            # Step 2: Dynamic Neo4j query
            structured_results = self.query_builder.execute_dynamic_query(query)
            
            # Step 3: Combine results
            return {
                "query": query,
                "semantic_context": semantic_results,
                "structured_relationships": structured_results,
                "generated_cypher": self.query_builder.build_dynamic_query(query)[0]
            }
        
        def _pinecone_search(self, query: str, org_id: str = None):
            # Your existing Pinecone search logic
            pass
    
    return KnowledgeEnrichedSearch()
