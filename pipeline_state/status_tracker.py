"""
Status tracking and monitoring for enterprise knowledge graph pipeline.

This module provides comprehensive status tracking, monitoring,
and visualization capabilities for the pipeline state management system.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .database import DatabaseManager
from .models import ProcessingStatus, StageType

logger = logging.getLogger(__name__)


@dataclass
class PipelineMetrics:
    """Pipeline performance metrics."""
    total_organizations: int
    total_files: int
    total_jobs: int
    successful_jobs: int
    failed_jobs: int
    running_jobs: int
    pending_jobs: int
    average_processing_time: float
    success_rate: float


class StatusTracker:
    """
    Comprehensive status tracking and monitoring for the pipeline.
    
    Provides real-time status monitoring, performance metrics,
    and visualization capabilities.
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize status tracker.
        
        Args:
            db_manager: Database manager instance
        """
        self.db = db_manager
    
    def get_pipeline_overview(self) -> Dict[str, Any]:
        """
        Get high-level pipeline overview.
        
        Returns:
            Pipeline overview dictionary
        """
        try:
            with self.db.get_session() as session:
                from .models import Organization, File, ProcessingJob, StageExecution
                
                # Basic counts
                total_orgs = session.query(Organization).count()
                total_files = session.query(File).count()
                total_jobs = session.query(ProcessingJob).count()
                
                # Job status counts
                job_status_counts = {}
                for status in ProcessingStatus:
                    count = session.query(ProcessingJob).filter_by(status=status).count()
                    job_status_counts[status.value] = count
                
                # File status counts
                file_status_counts = {}
                for status in ProcessingStatus:
                    count = session.query(File).filter_by(status=status).count()
                    file_status_counts[status.value] = count
                
                # Recent activity
                recent_jobs = session.query(ProcessingJob).order_by(
                    ProcessingJob.created_at.desc()
                ).limit(10).all()
                
                recent_activity = []
                for job in recent_jobs:
                    recent_activity.append({
                        "job_id": job.id,
                        "organization_id": job.organization_id,
                        "file_id": job.file_id,
                        "status": job.status.value,
                        "created_at": job.created_at.isoformat(),
                        "progress_percentage": job.progress_percentage
                    })
                
                return {
                    "overview": {
                        "total_organizations": total_orgs,
                        "total_files": total_files,
                        "total_jobs": total_jobs
                    },
                    "job_status_counts": job_status_counts,
                    "file_status_counts": file_status_counts,
                    "recent_activity": recent_activity,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Failed to get pipeline overview: {e}")
            return {"error": str(e)}
    
    def get_organization_dashboard(self, org_id: str) -> Dict[str, Any]:
        """
        Get comprehensive organization dashboard.
        
        Args:
            org_id: Organization ID
            
        Returns:
            Organization dashboard data
        """
        try:
            with self.db.get_session() as session:
                from .models import Organization, File, ProcessingJob, StageExecution
                
                org = session.query(Organization).filter_by(id=org_id).first()
                if not org:
                    return {"error": f"Organization {org_id} not found"}
                
                # File statistics
                files = session.query(File).filter_by(organization_id=org_id).all()
                file_stats = {
                    "total_files": len(files),
                    "by_status": {},
                    "by_data_source": {},
                    "total_size": 0
                }
                
                for file in files:
                    # Status counts
                    status = file.status.value
                    file_stats["by_status"][status] = file_stats["by_status"].get(status, 0) + 1
                    
                    # Data source counts
                    source_type = file.data_source.type.value if file.data_source else "unknown"
                    file_stats["by_data_source"][source_type] = file_stats["by_data_source"].get(source_type, 0) + 1
                    
                    # Total size
                    if file.file_size:
                        file_stats["total_size"] += file.file_size
                
                # Job statistics
                jobs = session.query(ProcessingJob).filter_by(organization_id=org_id).all()
                job_stats = {
                    "total_jobs": len(jobs),
                    "by_status": {},
                    "processing_times": [],
                    "success_rate": 0
                }
                
                successful_jobs = 0
                for job in jobs:
                    # Status counts
                    status = job.status.value
                    job_stats["by_status"][status] = job_stats["by_status"].get(status, 0) + 1
                    
                    # Processing times
                    if job.started_at and job.completed_at:
                        duration = (job.completed_at - job.started_at).total_seconds()
                        job_stats["processing_times"].append(duration)
                    
                    # Success count
                    if job.status == ProcessingStatus.COMPLETED:
                        successful_jobs += 1
                
                # Calculate success rate
                if len(jobs) > 0:
                    job_stats["success_rate"] = (successful_jobs / len(jobs)) * 100
                
                # Stage performance
                stage_performance = self._get_stage_performance(org_id)
                
                # Recent errors
                recent_errors = self._get_recent_errors(org_id, limit=5)
                
                return {
                    "organization": {
                        "id": org.id,
                        "name": org.name,
                        "description": org.description,
                        "created_at": org.created_at.isoformat()
                    },
                    "file_statistics": file_stats,
                    "job_statistics": job_stats,
                    "stage_performance": stage_performance,
                    "recent_errors": recent_errors,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Failed to get organization dashboard for {org_id}: {e}")
            return {"error": str(e)}
    
    def get_job_timeline(self, job_id: str) -> Dict[str, Any]:
        """
        Get detailed job execution timeline.
        
        Args:
            job_id: Job ID
            
        Returns:
            Job timeline data
        """
        try:
            with self.db.get_session() as session:
                from .models import ProcessingJob, StageExecution, ErrorLog
                
                job = session.query(ProcessingJob).filter_by(id=job_id).first()
                if not job:
                    return {"error": f"Job {job_id} not found"}
                
                # Get stage executions
                executions = session.query(StageExecution).filter_by(job_id=job_id).all()
                
                timeline = []
                for execution in executions:
                    stage_info = {
                        "execution_id": execution.id,
                        "stage_id": execution.stage_id,
                        "stage_name": execution.stage.name if execution.stage else "Unknown",
                        "stage_type": execution.stage.type.value if execution.stage else "unknown",
                        "status": execution.status.value,
                        "attempt_number": execution.attempt_number,
                        "created_at": execution.created_at.isoformat(),
                        "started_at": execution.started_at.isoformat() if execution.started_at else None,
                        "completed_at": execution.completed_at.isoformat() if execution.completed_at else None,
                        "duration_seconds": None,
                        "error_message": execution.error_message,
                        "result": execution.result,
                        "errors": []
                    }
                    
                    # Calculate duration
                    if execution.started_at and execution.completed_at:
                        duration = (execution.completed_at - execution.started_at).total_seconds()
                        stage_info["duration_seconds"] = duration
                    
                    # Get error logs
                    error_logs = session.query(ErrorLog).filter_by(stage_execution_id=execution.id).all()
                    for error_log in error_logs:
                        stage_info["errors"].append({
                            "error_type": error_log.error_type,
                            "error_message": error_log.error_message,
                            "is_recoverable": error_log.is_recoverable,
                            "recovery_suggestion": error_log.recovery_suggestion,
                            "created_at": error_log.created_at.isoformat()
                        })
                    
                    timeline.append(stage_info)
                
                # Sort by stage order
                timeline.sort(key=lambda x: x["stage_id"])
                
                return {
                    "job": {
                        "id": job.id,
                        "organization_id": job.organization_id,
                        "file_id": job.file_id,
                        "job_type": job.job_type,
                        "status": job.status.value,
                        "progress_percentage": job.progress_percentage,
                        "created_at": job.created_at.isoformat(),
                        "started_at": job.started_at.isoformat() if job.started_at else None,
                        "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                        "error_message": job.error_message
                    },
                    "timeline": timeline,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Failed to get job timeline for {job_id}: {e}")
            return {"error": str(e)}
    
    def get_performance_metrics(self, days: int = 7) -> PipelineMetrics:
        """
        Get pipeline performance metrics for the specified period.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            PipelineMetrics instance
        """
        try:
            with self.db.get_session() as session:
                from .models import Organization, File, ProcessingJob
                
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # Basic counts
                total_orgs = session.query(Organization).count()
                total_files = session.query(File).count()
                
                # Job metrics for the period
                jobs = session.query(ProcessingJob).filter(
                    ProcessingJob.created_at >= cutoff_date
                ).all()
                
                total_jobs = len(jobs)
                successful_jobs = sum(1 for job in jobs if job.status == ProcessingStatus.COMPLETED)
                failed_jobs = sum(1 for job in jobs if job.status == ProcessingStatus.FAILED)
                running_jobs = sum(1 for job in jobs if job.status == ProcessingStatus.RUNNING)
                pending_jobs = sum(1 for job in jobs if job.status == ProcessingStatus.PENDING)
                
                # Calculate average processing time
                processing_times = []
                for job in jobs:
                    if job.started_at and job.completed_at:
                        duration = (job.completed_at - job.started_at).total_seconds()
                        processing_times.append(duration)
                
                avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
                success_rate = (successful_jobs / total_jobs * 100) if total_jobs > 0 else 0
                
                return PipelineMetrics(
                    total_organizations=total_orgs,
                    total_files=total_files,
                    total_jobs=total_jobs,
                    successful_jobs=successful_jobs,
                    failed_jobs=failed_jobs,
                    running_jobs=running_jobs,
                    pending_jobs=pending_jobs,
                    average_processing_time=avg_processing_time,
                    success_rate=success_rate
                )
                
        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
            return PipelineMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0)

    def _get_stage_performance(self, org_id: str) -> Dict[str, Any]:
        """Get stage performance statistics for an organization."""
        try:
            with self.db.get_session() as session:
                from .models import ProcessingJob, StageExecution, PipelineStage

                # Get all stage executions for the organization
                executions = session.query(StageExecution).join(ProcessingJob).filter(
                    ProcessingJob.organization_id == org_id
                ).all()

                stage_stats = {}

                for execution in executions:
                    stage_name = execution.stage.name if execution.stage else "Unknown"

                    if stage_name not in stage_stats:
                        stage_stats[stage_name] = {
                            "total_executions": 0,
                            "successful": 0,
                            "failed": 0,
                            "average_duration": 0,
                            "durations": []
                        }

                    stats = stage_stats[stage_name]
                    stats["total_executions"] += 1

                    if execution.status == ProcessingStatus.COMPLETED:
                        stats["successful"] += 1
                    elif execution.status == ProcessingStatus.FAILED:
                        stats["failed"] += 1

                    # Calculate duration
                    if execution.started_at and execution.completed_at:
                        duration = (execution.completed_at - execution.started_at).total_seconds()
                        stats["durations"].append(duration)

                # Calculate averages
                for stage_name, stats in stage_stats.items():
                    if stats["durations"]:
                        stats["average_duration"] = sum(stats["durations"]) / len(stats["durations"])
                    del stats["durations"]  # Remove raw durations from output

                return stage_stats

        except Exception as e:
            logger.error(f"Failed to get stage performance for {org_id}: {e}")
            return {}

    def _get_recent_errors(self, org_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent errors for an organization."""
        try:
            with self.db.get_session() as session:
                from .models import ErrorLog, StageExecution, ProcessingJob

                # Get recent error logs for the organization
                error_logs = session.query(ErrorLog).join(StageExecution).join(ProcessingJob).filter(
                    ProcessingJob.organization_id == org_id
                ).order_by(ErrorLog.created_at.desc()).limit(limit).all()

                errors = []
                for error_log in error_logs:
                    errors.append({
                        "error_id": error_log.id,
                        "error_type": error_log.error_type,
                        "error_message": error_log.error_message,
                        "stage_name": error_log.stage_execution.stage.name if error_log.stage_execution.stage else "Unknown",
                        "job_id": error_log.stage_execution.job_id,
                        "file_id": error_log.stage_execution.job.file_id,
                        "is_recoverable": error_log.is_recoverable,
                        "recovery_suggestion": error_log.recovery_suggestion,
                        "created_at": error_log.created_at.isoformat()
                    })

                return errors

        except Exception as e:
            logger.error(f"Failed to get recent errors for {org_id}: {e}")
            return []

    def get_system_health(self) -> Dict[str, Any]:
        """
        Get overall system health status.

        Returns:
            System health dictionary
        """
        try:
            # Test database connection
            db_healthy = self.db.test_connection()

            # Get basic metrics
            metrics = self.get_performance_metrics(days=1)

            # Determine health status
            health_status = "healthy"
            issues = []

            if not db_healthy:
                health_status = "unhealthy"
                issues.append("Database connection failed")

            if metrics.success_rate < 80:
                health_status = "degraded" if health_status == "healthy" else health_status
                issues.append(f"Low success rate: {metrics.success_rate:.1f}%")

            if metrics.failed_jobs > metrics.successful_jobs:
                health_status = "degraded" if health_status == "healthy" else health_status
                issues.append("More failed jobs than successful jobs")

            return {
                "status": health_status,
                "database_healthy": db_healthy,
                "issues": issues,
                "metrics": {
                    "total_jobs_24h": metrics.total_jobs,
                    "success_rate_24h": metrics.success_rate,
                    "average_processing_time": metrics.average_processing_time,
                    "running_jobs": metrics.running_jobs,
                    "pending_jobs": metrics.pending_jobs
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get system health: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def generate_status_report(self, org_id: Optional[str] = None) -> str:
        """
        Generate a human-readable status report.

        Args:
            org_id: Optional organization ID for organization-specific report

        Returns:
            Formatted status report string
        """
        try:
            if org_id:
                # Organization-specific report
                dashboard = self.get_organization_dashboard(org_id)
                if "error" in dashboard:
                    return f"Error generating report for organization {org_id}: {dashboard['error']}"

                org = dashboard["organization"]
                file_stats = dashboard["file_statistics"]
                job_stats = dashboard["job_statistics"]

                report = f"""
ORGANIZATION STATUS REPORT
==========================
Organization: {org['name']} ({org['id']})
Generated: {dashboard['timestamp']}

FILE STATISTICS:
- Total Files: {file_stats['total_files']}
- Total Size: {file_stats['total_size']:,} bytes
- By Status: {', '.join(f"{k}: {v}" for k, v in file_stats['by_status'].items())}
- By Source: {', '.join(f"{k}: {v}" for k, v in file_stats['by_data_source'].items())}

JOB STATISTICS:
- Total Jobs: {job_stats['total_jobs']}
- Success Rate: {job_stats['success_rate']:.1f}%
- By Status: {', '.join(f"{k}: {v}" for k, v in job_stats['by_status'].items())}

RECENT ERRORS: {len(dashboard['recent_errors'])} errors found
"""

                for error in dashboard['recent_errors'][:3]:  # Show top 3 errors
                    report += f"- {error['error_type']}: {error['error_message'][:100]}...\n"

                return report.strip()

            else:
                # System-wide report
                overview = self.get_pipeline_overview()
                health = self.get_system_health()

                if "error" in overview:
                    return f"Error generating system report: {overview['error']}"

                report = f"""
PIPELINE SYSTEM STATUS REPORT
==============================
Generated: {overview['timestamp']}

SYSTEM HEALTH: {health['status'].upper()}
Database: {'✓ Healthy' if health['database_healthy'] else '✗ Unhealthy'}

OVERVIEW:
- Organizations: {overview['overview']['total_organizations']}
- Files: {overview['overview']['total_files']}
- Jobs: {overview['overview']['total_jobs']}

JOB STATUS:
{', '.join(f"{k}: {v}" for k, v in overview['job_status_counts'].items())}

FILE STATUS:
{', '.join(f"{k}: {v}" for k, v in overview['file_status_counts'].items())}

RECENT ACTIVITY: {len(overview['recent_activity'])} recent jobs
"""

                if health['issues']:
                    report += f"\nISSUES:\n"
                    for issue in health['issues']:
                        report += f"- {issue}\n"

                return report.strip()

        except Exception as e:
            logger.error(f"Failed to generate status report: {e}")
            return f"Error generating status report: {e}"
