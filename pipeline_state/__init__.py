"""
Pipeline State Management for Enterprise Knowledge Graph Processing

This module provides comprehensive state management for the enterprise KG pipeline,
tracking each stage of processing from data source connection to final storage.

Main Components:
- StateManager: Core state management and coordination
- PipelineOrchestrator: High-level pipeline execution
- StatusTracker: Status monitoring and visualization
- Database models: PostgreSQL schema for state tracking

Usage:
    from enterprise_kg.pipeline_state import PipelineOrchestrator, StateManager
    
    # Initialize pipeline
    orchestrator = PipelineOrchestrator(
        postgres_config={
            "host": "localhost",
            "port": 5432,
            "database": "enterprise_kg",
            "username": "postgres",
            "password": "password"
        }
    )
    
    # Process organization data
    result = orchestrator.process_organization(
        org_id="rapid_innovation",
        org_name="Rapid Innovation",
        data_sources=[
            {"type": "slack", "config": {...}},
            {"type": "jira", "config": {...}},
            {"type": "files", "path": "/documents"}
        ]
    )
"""

from .state_manager import StateManager
from .pipeline_orchestrator import PipelineOrchestrator
from .status_tracker import StatusTracker
from .models import (
    Organization,
    DataSource,
    File,
    ProcessingJob,
    PipelineStage,
    StageExecution,
    ErrorLog
)
from .database import DatabaseManager

__version__ = "1.0.0"
__all__ = [
    "StateManager",
    "PipelineOrchestrator", 
    "StatusTracker",
    "DatabaseManager",
    "Organization",
    "DataSource",
    "File",
    "ProcessingJob",
    "PipelineStage",
    "StageExecution",
    "ErrorLog"
]
