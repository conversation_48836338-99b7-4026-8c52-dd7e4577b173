# Enterprise KG Pipeline State Management

A comprehensive pipeline state management system for enterprise knowledge graph processing that tracks each stage of processing from data source connection to final storage.

## Overview

This module provides robust state management for the enterprise knowledge graph pipeline with the following key features:

- **Multi-stage Pipeline Tracking**: Monitor each stage of the processing pipeline
- **Resumable Processing**: Restart from any failed stage without losing progress
- **Parallel Processing Support**: Handle multiple files simultaneously with configurable workers
- **Comprehensive Error Tracking**: Detailed error logging with recovery suggestions
- **Real-time Status Monitoring**: Live pipeline status and performance metrics
- **PostgreSQL State Storage**: Reliable state persistence with full audit trail

## Pipeline Stages

The system manages five key stages:

1. **Data Source Connection** - Connect to data sources (Slack, Jira, files) and collect content
2. **File Attachment** - Attach files to organization with 'contains' relations in Neo4j
3. **Document Processing** - Process files through enterprise_kg_minimal module for chunking
4. **Graph Generation** - Convert chunks to graphs and attach to existing file nodes
5. **Vector Storage** - Insert chunks into Pinecone with embeddings and metadata

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │   Neo4j Graph    │    │    Pinecone     │
│  State Storage  │    │    Database      │    │ Vector Storage  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │         Pipeline Orchestrator                   │
         │  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
         │  │ State Mgr   │  │Status Track │  │Database │ │
         │  └─────────────┘  └─────────────┘  └─────────┘ │
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Data Sources                       │
         │  ┌─────────┐  ┌─────────┐  ┌─────────────────┐  │
         │  │  Files  │  │  Slack  │  │      Jira       │  │
         │  └─────────┘  └─────────┘  └─────────────────┘  │
         └─────────────────────────────────────────────────┘
```

## Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements_pipeline.txt

# Setup PostgreSQL database
createdb enterprise_kg

# Run database migrations
psql -d enterprise_kg -f pipeline_state/migrations/001_initial_schema.sql
```

### 2. Configuration

Set up your environment variables:

```bash
# PostgreSQL
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DB=enterprise_kg
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=your_password

# Neo4j
export NEO4J_URI=bolt://localhost:7687
export NEO4J_USER=neo4j
export NEO4J_PASSWORD=your_password

# LLM (using verified Requesty model)
export REQUESTY_API_KEY=your_requesty_api_key

# Optional: Pinecone
export PINECONE_API_KEY=your_pinecone_api_key
export PINECONE_ENVIRONMENT=us-west1-gcp
```

### 3. Basic Usage

```python
from enterprise_kg.pipeline_state import PipelineOrchestrator

# Initialize orchestrator
orchestrator = PipelineOrchestrator(
    postgres_config={
        "host": "localhost",
        "port": 5432,
        "database": "enterprise_kg",
        "username": "postgres",
        "password": "password"
    }
)

# Process organization data
result = orchestrator.process_organization(
    org_id="rapid_innovation",
    org_name="Rapid Innovation",
    data_sources=[
        {
            "type": "files",
            "name": "Document Repository",
            "config": {"path": "documents"}
        }
    ],
    neo4j_config={
        "uri": "bolt://localhost:7687",
        "user": "neo4j",
        "password": "password"
    },
    llm_config={
        "provider": "requesty",
        "model": "claude-3-5-sonnet-20241022",
        "api_key": "your-api-key"
    }
)
```

## Status Monitoring

### Real-time Status Tracking

```python
from enterprise_kg.pipeline_state import StatusTracker

status_tracker = StatusTracker(orchestrator.db_manager)

# Get system overview
overview = status_tracker.get_pipeline_overview()

# Get organization dashboard
dashboard = status_tracker.get_organization_dashboard("rapid_innovation")

# Get job timeline
timeline = status_tracker.get_job_timeline("job_id")

# Generate status report
report = status_tracker.generate_status_report("rapid_innovation")
print(report)
```

### Performance Metrics

```python
# Get performance metrics
metrics = status_tracker.get_performance_metrics(days=7)
print(f"Success rate: {metrics.success_rate:.1f}%")
print(f"Average processing time: {metrics.average_processing_time:.1f}s")

# Check system health
health = status_tracker.get_system_health()
print(f"System status: {health['status']}")
```

## Error Handling and Recovery

### Automatic Retry

```python
# Retry failed jobs for an organization
retry_result = orchestrator.retry_failed_jobs("rapid_innovation")
print(f"Retried {retry_result['retried_jobs']} jobs")
```

### Error Analysis

```python
# Get detailed job timeline with errors
timeline = status_tracker.get_job_timeline("failed_job_id")
for stage in timeline["timeline"]:
    if stage["errors"]:
        for error in stage["errors"]:
            print(f"Error: {error['error_message']}")
            print(f"Recovery: {error['recovery_suggestion']}")
```

## Database Schema

The system uses PostgreSQL with the following key tables:

- **organizations** - Organization metadata and configurations
- **data_sources** - Data source configurations (Slack, Jira, files)
- **files** - File metadata and processing status
- **processing_jobs** - Individual processing job tracking
- **pipeline_stages** - Stage definitions and configurations
- **stage_executions** - Individual stage execution logs
- **error_logs** - Detailed error tracking and recovery info

## Integration with Enterprise KG Minimal

The pipeline seamlessly integrates with the existing `enterprise_kg_minimal` module:

1. **File Processing**: Uses `process_document()` function for document chunking and graph generation
2. **Neo4j Integration**: Leverages existing Neo4j client for graph storage
3. **Modular Design**: Works alongside existing enterprise_kg components

## Configuration Options

### Pipeline Orchestrator

```python
orchestrator = PipelineOrchestrator(
    postgres_config=postgres_config,
    max_workers=4,                    # Parallel processing workers
    enable_parallel_processing=True   # Enable/disable parallel execution
)
```

### Data Source Types

- **files**: Local file system
- **slack**: Slack workspace (placeholder for future implementation)
- **jira**: Jira instance (placeholder for future implementation)
- **confluence**: Confluence space (future)
- **sharepoint**: SharePoint site (future)
- **google_drive**: Google Drive folder (future)

## Example Usage

See `pipeline_example.py` for a complete working example that demonstrates:

- Organization setup and configuration
- File processing through all pipeline stages
- Status monitoring and reporting
- Error handling and retry mechanisms
- Performance metrics and health checks

## Development and Testing

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run tests
pytest pipeline_state/tests/

# Run with coverage
pytest --cov=pipeline_state pipeline_state/tests/
```

### Database Management

```bash
# Create test database
createdb enterprise_kg_test

# Run migrations
psql -d enterprise_kg_test -f pipeline_state/migrations/001_initial_schema.sql

# Clean up old data (30+ days)
python -c "
from pipeline_state import DatabaseManager
db = DatabaseManager(config)
db.cleanup_old_data(days=30)
"
```

## Production Deployment

### Monitoring Setup

1. **Health Checks**: Implement periodic health monitoring
2. **Alerting**: Set up alerts for failed jobs and system issues
3. **Metrics Collection**: Export metrics to monitoring systems
4. **Log Aggregation**: Centralize logs for analysis

### Performance Tuning

1. **Database Indexing**: Ensure proper indexes for query performance
2. **Connection Pooling**: Configure appropriate connection pool sizes
3. **Parallel Processing**: Tune worker count based on system resources
4. **Batch Processing**: Process multiple files in batches for efficiency

## Troubleshooting

### Common Issues

1. **Database Connection**: Verify PostgreSQL is running and accessible
2. **Neo4j Connection**: Check Neo4j credentials and network connectivity
3. **LLM API**: Ensure API keys are valid and have sufficient quota
4. **File Permissions**: Verify read access to document directories

### Debug Mode

Enable detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

- **Slack Integration**: Complete Slack API integration for message processing
- **Jira Integration**: Full Jira API integration for issue and comment processing
- **Web Dashboard**: Real-time web-based monitoring dashboard
- **API Endpoints**: REST API for external system integration
- **Workflow Orchestration**: Advanced workflow management with dependencies
- **Data Lineage**: Track data lineage and processing history
