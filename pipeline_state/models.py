"""
SQLAlchemy models for pipeline state management.

This module defines the PostgreSQL database schema for tracking
enterprise knowledge graph processing pipeline state.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
import json

from sqlalchemy import (
    Column, Integer, String, DateTime, Boolean, Text, JSON,
    ForeignKey, Enum as SQLEnum, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class ProcessingStatus(Enum):
    """Status enumeration for processing stages."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"


class DataSourceType(Enum):
    """Types of data sources."""
    SLACK = "slack"
    JIRA = "jira"
    FILES = "files"
    CONFLUENCE = "confluence"
    SHAREPOINT = "sharepoint"
    GOOGLE_DRIVE = "google_drive"


class StageType(Enum):
    """Pipeline stage types."""
    DATA_SOURCE_CONNECTION = "data_source_connection"
    FILE_ATTACHMENT = "file_attachment"
    DOCUMENT_PROCESSING = "document_processing"
    GRAPH_GENERATION = "graph_generation"
    VECTOR_STORAGE = "vector_storage"


class Organization(Base):
    """Organization entity."""
    __tablename__ = "organizations"
    
    id = Column(String(255), primary_key=True)
    name = Column(String(500), nullable=False)
    description = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Configuration
    neo4j_config = Column(JSON)
    pinecone_config = Column(JSON)
    llm_config = Column(JSON)
    
    # Relationships
    data_sources = relationship("DataSource", back_populates="organization")
    files = relationship("File", back_populates="organization")
    processing_jobs = relationship("ProcessingJob", back_populates="organization")


class DataSource(Base):
    """Data source configuration."""
    __tablename__ = "data_sources"
    
    id = Column(String(255), primary_key=True)
    organization_id = Column(String(255), ForeignKey("organizations.id"), nullable=False)
    name = Column(String(500), nullable=False)
    type = Column(SQLEnum(DataSourceType), nullable=False)
    
    # Configuration
    config = Column(JSON)  # Source-specific configuration
    credentials = Column(JSON)  # Encrypted credentials
    
    # Status
    status = Column(SQLEnum(ProcessingStatus), default=ProcessingStatus.PENDING)
    last_sync = Column(DateTime)
    next_sync = Column(DateTime)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization", back_populates="data_sources")
    files = relationship("File", back_populates="data_source")


class File(Base):
    """File/content entity."""
    __tablename__ = "files"
    
    id = Column(String(255), primary_key=True)
    organization_id = Column(String(255), ForeignKey("organizations.id"), nullable=False)
    data_source_id = Column(String(255), ForeignKey("data_sources.id"), nullable=False)
    
    # File metadata
    filename = Column(String(1000), nullable=False)
    file_path = Column(Text)
    file_size = Column(Integer)
    content_type = Column(String(100))
    content_hash = Column(String(64))  # SHA-256 hash
    
    # Content
    content = Column(Text)  # For text files or extracted content
    metadata = Column(JSON)  # File-specific metadata
    
    # Processing status
    status = Column(SQLEnum(ProcessingStatus), default=ProcessingStatus.PENDING)
    neo4j_node_created = Column(Boolean, default=False)
    chunks_created = Column(Integer, default=0)
    embeddings_created = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_processed = Column(DateTime)
    
    # Relationships
    organization = relationship("Organization", back_populates="files")
    data_source = relationship("DataSource", back_populates="files")
    processing_jobs = relationship("ProcessingJob", back_populates="file")


class ProcessingJob(Base):
    """Individual processing job tracking."""
    __tablename__ = "processing_jobs"
    
    id = Column(String(255), primary_key=True)
    organization_id = Column(String(255), ForeignKey("organizations.id"), nullable=False)
    file_id = Column(String(255), ForeignKey("files.id"), nullable=False)
    
    # Job configuration
    job_type = Column(String(100), nullable=False)  # full_pipeline, retry_stage, etc.
    config = Column(JSON)  # Job-specific configuration
    
    # Status tracking
    status = Column(SQLEnum(ProcessingStatus), default=ProcessingStatus.PENDING)
    current_stage = Column(SQLEnum(StageType))
    progress_percentage = Column(Integer, default=0)
    
    # Results
    result = Column(JSON)  # Job results and metrics
    error_message = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Relationships
    organization = relationship("Organization", back_populates="processing_jobs")
    file = relationship("File", back_populates="processing_jobs")
    stage_executions = relationship("StageExecution", back_populates="job")


class PipelineStage(Base):
    """Pipeline stage definitions."""
    __tablename__ = "pipeline_stages"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, unique=True)
    type = Column(SQLEnum(StageType), nullable=False)
    description = Column(Text)
    
    # Configuration
    order_index = Column(Integer, nullable=False)
    is_parallel = Column(Boolean, default=False)
    retry_count = Column(Integer, default=3)
    timeout_seconds = Column(Integer, default=3600)
    
    # Dependencies
    depends_on = Column(JSON)  # List of stage IDs this stage depends on
    
    created_at = Column(DateTime, default=func.now())


class StageExecution(Base):
    """Individual stage execution tracking."""
    __tablename__ = "stage_executions"
    
    id = Column(String(255), primary_key=True)
    job_id = Column(String(255), ForeignKey("processing_jobs.id"), nullable=False)
    stage_id = Column(Integer, ForeignKey("pipeline_stages.id"), nullable=False)
    
    # Execution details
    status = Column(SQLEnum(ProcessingStatus), default=ProcessingStatus.PENDING)
    attempt_number = Column(Integer, default=1)
    
    # Results
    result = Column(JSON)
    error_message = Column(Text)
    logs = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Relationships
    job = relationship("ProcessingJob", back_populates="stage_executions")
    stage = relationship("PipelineStage")
    error_logs = relationship("ErrorLog", back_populates="stage_execution")


class ErrorLog(Base):
    """Detailed error logging."""
    __tablename__ = "error_logs"
    
    id = Column(String(255), primary_key=True)
    stage_execution_id = Column(String(255), ForeignKey("stage_executions.id"))
    
    # Error details
    error_type = Column(String(200))
    error_message = Column(Text)
    stack_trace = Column(Text)
    context = Column(JSON)  # Additional context for debugging
    
    # Recovery information
    is_recoverable = Column(Boolean, default=True)
    recovery_suggestion = Column(Text)
    
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    stage_execution = relationship("StageExecution", back_populates="error_logs")


# Indexes for performance
Index("idx_files_org_status", File.organization_id, File.status)
Index("idx_jobs_org_status", ProcessingJob.organization_id, ProcessingJob.status)
Index("idx_stage_exec_job_stage", StageExecution.job_id, StageExecution.stage_id)
Index("idx_error_logs_stage", ErrorLog.stage_execution_id)
