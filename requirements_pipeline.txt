# Pipeline State Management Requirements
# Install with: pip install -r requirements_pipeline.txt

# Core pipeline state management dependencies
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
alembic>=1.12.0

# Existing enterprise_kg dependencies
neo4j>=5.0.0
python-dotenv>=1.0.0

# LLM providers (choose one or more)
openai>=1.0.0
anthropic>=0.7.0

# Optional: For Pinecone integration
pinecone-client>=2.2.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0

# Code formatting
black>=23.0.0
isort>=5.0.0
