#!/usr/bin/env python3
"""
Debug script to check what's actually in Neo4j database
"""

import os
from dotenv import load_dotenv

load_dotenv()

def debug_neo4j():
    try:
        from neo4j import GraphDatabase
        
        uri = os.getenv('NEO4J_URI')
        user = os.getenv('NEO4J_USER') 
        password = os.getenv('NEO4J_PASSWORD')
        
        print(f"🔗 Connecting to Neo4j at {uri}")
        
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            print("\n📊 Database Contents:")
            print("=" * 50)
            
            # Check all node types
            result = session.run('MATCH (n) RETURN DISTINCT labels(n) as labels, count(n) as count')
            print("Node Types:")
            for record in result:
                print(f"   {record['labels']}: {record['count']}")
            
            # Check all relationship types
            result = session.run('MATCH ()-[r]->() RETURN DISTINCT type(r) as rel_type, count(r) as count')
            print("\nRelationship Types:")
            for record in result:
                print(f"   {record['rel_type']}: {record['count']}")
            
            # Look for our test file
            result = session.run('MATCH (f:File) WHERE f.id CONTAINS "techcorp" RETURN f.id, f.name')
            print("\nTest Files Found:")
            files = list(result)
            if files:
                for record in files:
                    print(f"   ID: {record['f.id']}")
                    print(f"   Name: {record.get('f.name', 'N/A')}")
            else:
                print("   No files found with 'techcorp' in ID")
            
            # Look for any File nodes
            result = session.run('MATCH (f:File) RETURN f.id LIMIT 5')
            print("\nAll File Nodes (first 5):")
            for record in result:
                print(f"   {record['f.id']}")
            
            # Look for chunks
            result = session.run('MATCH (c:Chunk) WHERE c.id CONTAINS "techcorp" RETURN count(c) as count')
            chunk_count = result.single()
            if chunk_count:
                print(f"\nChunks with 'techcorp': {chunk_count['count']}")
            
            # Look for entities
            result = session.run('MATCH (e:Entity) RETURN count(e) as count')
            entity_count = result.single()
            if entity_count:
                print(f"Total Entities: {entity_count['count']}")
            
            # Check specific relationships
            print("\n🔗 Relationship Analysis:")
            
            # CONTAINS relationships
            result = session.run('MATCH ()-[r:CONTAINS]->() RETURN count(r) as count')
            contains_count = result.single()['count']
            print(f"   CONTAINS relationships: {contains_count}")
            
            # EXTRACTED_FROM relationships  
            result = session.run('MATCH ()-[r:EXTRACTED_FROM]->() RETURN count(r) as count')
            extracted_count = result.single()['count']
            print(f"   EXTRACTED_FROM relationships: {extracted_count}")
            
            # Show sample CONTAINS relationship
            result = session.run('MATCH (f)-[r:CONTAINS]->(c) RETURN f.id, c.id LIMIT 3')
            print("\nSample CONTAINS relationships:")
            for record in result:
                print(f"   {record['f.id']} -[CONTAINS]-> {record['c.id']}")
            
            # Show sample EXTRACTED_FROM relationship
            result = session.run('MATCH (c)-[r:EXTRACTED_FROM]->(e) RETURN c.id, e.name LIMIT 3')
            print("\nSample EXTRACTED_FROM relationships:")
            for record in result:
                print(f"   {record['c.id']} -[EXTRACTED_FROM]-> {record['e.name']}")
        
        driver.close()
        
        print(f"\n🔍 Suggested Queries:")
        print("Try these in Neo4j Browser:")
        print()
        print("1. Check all nodes:")
        print("   MATCH (n) RETURN labels(n), count(n)")
        print()
        print("2. Check all relationships:")
        print("   MATCH ()-[r]->() RETURN type(r), count(r)")
        print()
        print("3. Find your file:")
        print("   MATCH (f:File) RETURN f")
        print()
        print("4. View file and chunks:")
        print("   MATCH (f:File)-[:CONTAINS]->(c:Chunk) RETURN f, c")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_neo4j()
