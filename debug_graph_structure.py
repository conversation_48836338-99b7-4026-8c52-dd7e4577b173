#!/usr/bin/env python3
"""
Debug Graph Structure

This script examines the actual structure of the knowledge graph
to understand why connectivity analysis is failing.

Usage:
    python debug_graph_structure.py
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def debug_graph_structure():
    """Debug the actual graph structure in Neo4j."""
    
    # Get Neo4j configuration
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    print("🔍 Debugging Graph Structure")
    print("=" * 50)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            neo4j_uri,
            auth=(neo4j_user, neo4j_password)
        )
        
        with driver.session() as session:
            
            # 1. Count all node types
            print("1️⃣ Node Types and Counts:")
            result = session.run("""
                MATCH (n)
                RETURN labels(n) as labels, count(n) as count
                ORDER BY count DESC
            """)
            for record in result:
                print(f"   {record['labels']}: {record['count']}")
            
            # 2. Count all relationship types
            print("\n2️⃣ Relationship Types and Counts:")
            result = session.run("""
                MATCH ()-[r]->()
                RETURN type(r) as rel_type, count(r) as count
                ORDER BY count DESC
            """)
            for record in result:
                print(f"   {record['rel_type']}: {record['count']}")
            
            # 3. Check File-Chunk structure
            print("\n3️⃣ File-Chunk Structure:")
            result = session.run("""
                MATCH (f:File)-[r:CONTAINS]->(c:Chunk)
                RETURN f.id as file_id, count(c) as chunk_count
            """)
            for record in result:
                print(f"   File '{record['file_id']}' contains {record['chunk_count']} chunks")
            
            # 4. Check Chunk-Entity structure
            print("\n4️⃣ Chunk-Entity Structure:")
            result = session.run("""
                MATCH (c:Chunk)-[r:EXTRACTED_FROM]->(e:Entity)
                RETURN count(DISTINCT c) as chunks_with_entities, count(DISTINCT e) as total_entities
            """)
            record = result.single()
            if record:
                print(f"   Chunks with entities: {record['chunks_with_entities']}")
                print(f"   Total entities: {record['total_entities']}")
            
            # 5. Sample entities
            print("\n5️⃣ Sample Entities:")
            result = session.run("""
                MATCH (e:Entity)
                RETURN e.name, e.entity_type
                LIMIT 10
            """)
            for record in result:
                print(f"   {record['e.name']} ({record['e.entity_type']})")
            
            # 6. Entity-Entity relationships
            print("\n6️⃣ Entity-Entity Relationships:")
            result = session.run("""
                MATCH (e1:Entity)-[r]->(e2:Entity)
                WHERE type(r) <> 'EXTRACTED_FROM'
                RETURN type(r) as rel_type, count(r) as count
                ORDER BY count DESC
            """)
            total_entity_rels = 0
            for record in result:
                print(f"   {record['rel_type']}: {record['count']}")
                total_entity_rels += record['count']
            
            if total_entity_rels == 0:
                print("   ❌ No entity-entity relationships found!")
            
            # 7. Sample entity relationships
            print("\n7️⃣ Sample Entity Relationships:")
            result = session.run("""
                MATCH (e1:Entity)-[r]->(e2:Entity)
                WHERE type(r) <> 'EXTRACTED_FROM'
                RETURN e1.name, type(r), e2.name
                LIMIT 10
            """)
            count = 0
            for record in result:
                print(f"   {record['e1.name']} --{record['type(r)']}--> {record['e2.name']}")
                count += 1
            
            if count == 0:
                print("   ❌ No entity relationships found!")
            
            # 8. Check all nodes that might be entities
            print("\n8️⃣ All Non-File/Chunk Nodes (potential entities):")
            result = session.run("""
                MATCH (n)
                WHERE NOT n:File AND NOT n:Chunk
                RETURN labels(n) as labels, n.name as name, count(*) as count
                ORDER BY count DESC
                LIMIT 20
            """)
            for record in result:
                print(f"   {record['name']} - Labels: {record['labels']}")

            # 9. Check relationships between non-File/Chunk nodes
            print("\n9️⃣ Relationships between entities:")
            result = session.run("""
                MATCH (n1)-[r]->(n2)
                WHERE NOT n1:File AND NOT n1:Chunk AND NOT n2:File AND NOT n2:Chunk
                RETURN type(r) as rel_type, count(r) as count
                ORDER BY count DESC
            """)
            for record in result:
                print(f"   {record['rel_type']}: {record['count']}")
            
            # 10. Check how chunks connect to entities
            print("\n🔟 Chunk-Entity Connections:")
            result = session.run("""
                MATCH (c:Chunk)-[r]->(n)
                WHERE NOT n:File AND NOT n:Chunk
                RETURN type(r) as rel_type, count(r) as count
                ORDER BY count DESC
            """)
            chunk_entity_rels = 0
            for record in result:
                print(f"   {record['rel_type']}: {record['count']}")
                chunk_entity_rels += record['count']

            if chunk_entity_rels == 0:
                print("   ❌ No chunk-entity relationships found!")

            # 11. Sample chunk-entity connections
            print("\n1️⃣1️⃣ Sample Chunk-Entity Connections:")
            result = session.run("""
                MATCH (c:Chunk)-[r]->(n)
                WHERE NOT n:File AND NOT n:Chunk
                RETURN c.id as chunk_id, type(r) as rel_type, n.name as entity_name, labels(n) as entity_labels
                LIMIT 10
            """)
            for record in result:
                print(f"   {record['chunk_id']} --{record['rel_type']}--> {record['entity_name']} {record['entity_labels']}")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Error debugging graph: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    
    success = debug_graph_structure()
    
    if success:
        print(f"\n🎯 Debug completed!")
        print(f"   Check the output above to understand the graph structure")
    else:
        print(f"\n💥 Debug failed")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
