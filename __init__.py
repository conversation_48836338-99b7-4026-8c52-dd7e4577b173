"""
Enterprise Knowledge Graph (EnterpriseKG)

A custom enterprise data management system for extracting entities and relationships
from enterprise documents and building knowledge graphs.
"""

from .flows.enterprise_flow import create_enterprise_flow, EnterpriseKGFlow
from .constants.entities import EntityType
from .constants.relationships import RelationshipType
from .constants.schemas import EntityRelationship, DocumentSummary

__version__ = "0.1.0"
__author__ = "Enterprise KG Team"

__all__ = [
    "create_enterprise_flow",
    "EnterpriseKGFlow", 
    "EntityType",
    "RelationshipType",
    "EntityRelationship",
    "DocumentSummary",
]
