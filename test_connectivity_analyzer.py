#!/usr/bin/env python3
"""
Test Script for Graph Connectivity Analyzer

This script tests the graph connectivity analyzer with sample data
without requiring external dependencies like Neo4j or LLM APIs.

Usage:
    python test_connectivity_analyzer.py
"""

import sys
from typing import Dict, List, Any

def create_mock_neo4j_client():
    """Create a mock Neo4j client for testing."""
    
    class MockConnection:
        def __init__(self):
            self.database = None
    
    class MockSession:
        def __init__(self, sample_data):
            self.sample_data = sample_data
        
        def run(self, query, **params):
            # Return mock results based on query type
            if "MATCH (n)" in query and "RETURN DISTINCT" in query:
                # Mock nodes query
                return MockResult([
                    {"name": "culture_values_rapid", "labels": ["File"], "properties": {"id": "culture_values_rapid"}},
                    {"name": "culture_values_rapid_chunk_0", "labels": ["Chunk"], "properties": {"id": "culture_values_rapid_chunk_0"}},
                    {"name": "culture_values_rapid_chunk_1", "labels": ["Chunk"], "properties": {"id": "culture_values_rapid_chunk_1"}},
                    {"name": "Rapid Innovation", "labels": ["Entity"], "properties": {"name": "Rapid Innovation", "entity_type": "Organization"}},
                    {"name": "Company Culture", "labels": ["Entity"], "properties": {"name": "Company Culture", "entity_type": "Concept"}},
                    {"name": "Innovation", "labels": ["Entity"], "properties": {"name": "Innovation", "entity_type": "Concept"}},
                    {"name": "Team Collaboration", "labels": ["Entity"], "properties": {"name": "Team Collaboration", "entity_type": "Concept"}},
                ])
            elif "MATCH (n1)-[r]->(n2)" in query:
                # Mock relationships query
                return MockResult([
                    {"source": "culture_values_rapid", "target": "culture_values_rapid_chunk_0", "rel_type": "CONTAINS"},
                    {"source": "culture_values_rapid", "target": "culture_values_rapid_chunk_1", "rel_type": "CONTAINS"},
                    {"source": "culture_values_rapid_chunk_0", "target": "Rapid Innovation", "rel_type": "EXTRACTED_FROM"},
                    {"source": "culture_values_rapid_chunk_0", "target": "Company Culture", "rel_type": "EXTRACTED_FROM"},
                    {"source": "culture_values_rapid_chunk_1", "target": "Innovation", "rel_type": "EXTRACTED_FROM"},
                    {"source": "culture_values_rapid_chunk_1", "target": "Team Collaboration", "rel_type": "EXTRACTED_FROM"},
                    {"source": "Rapid Innovation", "target": "Innovation", "rel_type": "FOCUSES_ON"},
                    {"source": "Company Culture", "target": "Team Collaboration", "rel_type": "PROMOTES"},
                ])
            elif "MATCH (f:File" in query and "OPTIONAL MATCH" in query:
                # Mock file-chunk analysis query
                return MockResult([
                    {
                        "file_id": "culture_values_rapid",
                        "chunk_count": 2,
                        "entity_count": 4,
                        "chunk_ids": ["culture_values_rapid_chunk_0", "culture_values_rapid_chunk_1"]
                    }
                ])
            else:
                return MockResult([])
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            pass
    
    class MockResult:
        def __init__(self, records):
            self.records = records
            self.index = 0
        
        def __iter__(self):
            return iter(self.records)
        
        def single(self):
            return self.records[0] if self.records else None
    
    class MockDriver:
        def session(self, database=None):
            return MockSession({})
        
        def close(self):
            pass
    
    class MockNeo4jClient:
        def __init__(self):
            self.connection = MockConnection()
        
        def _get_driver(self):
            return MockDriver()
    
    return MockNeo4jClient()

def test_connectivity_analyzer():
    """Test the graph connectivity analyzer with mock data."""
    
    print("🧪 Testing Graph Connectivity Analyzer")
    print("=" * 50)
    
    try:
        # Import the analyzer
        from enterprise_kg_minimal.utils.graph_analyzer import GraphConnectivityAnalyzer
        
        # Create mock Neo4j client
        mock_client = create_mock_neo4j_client()
        
        # Create analyzer
        analyzer = GraphConnectivityAnalyzer(mock_client)
        
        print("✅ Successfully imported GraphConnectivityAnalyzer")
        print("✅ Created analyzer with mock Neo4j client")
        
        # Test connectivity analysis
        print("\n🔍 Running connectivity analysis...")
        
        result = analyzer.analyze_graph_connectivity(
            file_id="culture_values_rapid",
            include_file_chunk_structure=True
        )
        
        print("✅ Connectivity analysis completed")
        
        # Display results
        analyzer.print_connectivity_report(result, "culture_values_rapid")
        
        # Test summary
        print(f"\n📋 Test Summary:")
        print(f"   Nodes found: {result.total_nodes}")
        print(f"   Relationships found: {result.total_relationships}")
        print(f"   Connected components: {result.connected_components}")
        print(f"   Graph is connected: {result.is_connected}")
        print(f"   Connectivity ratio: {result.connectivity_ratio:.2%}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure you're running from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_imports():
    """Test basic imports from enterprise_kg_minimal."""
    
    print("\n🔧 Testing Basic Imports")
    print("=" * 30)
    
    try:
        # Test main import
        from enterprise_kg_minimal import process_document
        print("✅ Successfully imported process_document")
        
        # Test utils imports
        from enterprise_kg_minimal.utils import GraphConnectivityAnalyzer
        print("✅ Successfully imported GraphConnectivityAnalyzer from utils")
        
        # Test storage imports
        from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient
        print("✅ Successfully imported Neo4jClient")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def show_usage_example():
    """Show example usage of the connectivity analyzer."""
    
    print(f"\n📖 Usage Example:")
    print("=" * 30)
    print("""
# Example: Analyze graph connectivity for a processed document

from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection
from enterprise_kg_minimal.utils.graph_analyzer import GraphConnectivityAnalyzer

# Create Neo4j client
neo4j_conn = Neo4jConnection(
    uri="bolt://localhost:7687",
    user="neo4j",
    password="password"
)
neo4j_client = Neo4jClient(neo4j_conn)

# Create analyzer
analyzer = GraphConnectivityAnalyzer(neo4j_client)

# Analyze connectivity for a specific file
result = analyzer.analyze_graph_connectivity(file_id="your_file_id")

# Print detailed report
analyzer.print_connectivity_report(result, "your_file_id")

# Check if graph is connected
if result.is_connected:
    print("✅ Graph is fully connected!")
else:
    print(f"❌ Graph has {result.connected_components} separate components")
""")

def main():
    """Main test function."""
    
    print("🧪 Enterprise KG Connectivity Analyzer Test Suite")
    print("=" * 60)
    
    # Test basic imports
    imports_ok = test_basic_imports()
    
    if imports_ok:
        # Test connectivity analyzer
        analyzer_ok = test_connectivity_analyzer()
        
        if analyzer_ok:
            # Show usage example
            show_usage_example()
            
            print(f"\n🎉 All tests passed!")
            print("   The GraphConnectivityAnalyzer is ready to use")
            print("   Run 'python run_pdf_analysis.py' to analyze the PDF document")
            return True
        else:
            print(f"\n💥 Connectivity analyzer test failed")
    else:
        print(f"\n💥 Import tests failed")
        print("   Make sure you're in the correct directory and enterprise_kg_minimal is available")
    
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
