#!/usr/bin/env python3
"""
PDF Document Analysis with Knowledge Graph Connectivity Check

This script processes the PDF document in enterprise_kg_minimal/documents/
and analyzes the connectivity of the resulting knowledge graph.

Features:
- Extracts text from PDF document
- Processes document through enterprise_kg_minimal pipeline
- Creates knowledge graph in Neo4j
- Analyzes graph connectivity
- Provides detailed connectivity report

Prerequisites:
1. Neo4j running (local or cloud)
2. LLM API key (OpenAI, Anthropic, or Requesty)
3. Required packages: neo4j, python-dotenv, pypdf

Usage:
    python run_pdf_analysis.py
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def extract_pdf_content(pdf_path: str) -> str:
    """Extract text content from PDF file."""
    try:
        import pypdf
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = pypdf.PdfReader(file)
            text_content = ""
            
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                text_content += f"\n--- Page {page_num + 1} ---\n"
                text_content += page_text
                
        return text_content.strip()
        
    except ImportError:
        print("❌ pypdf not installed. Install with: pip install pypdf")
        return None
    except Exception as e:
        print(f"❌ Error extracting PDF content: {e}")
        return None

def setup_configuration():
    """Setup Neo4j and LLM configuration from environment variables."""
    # Neo4j configuration
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    # LLM configuration - try multiple providers
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    requesty_key = os.getenv("REQUESTY_API_KEY")
    
    if openai_key:
        llm_config = {
            "llm_provider": "openai",
            "llm_model": "gpt-4o",
            "llm_api_key": openai_key
        }
        print(f"🤖 LLM: OpenAI GPT-4o")
    elif anthropic_key:
        llm_config = {
            "llm_provider": "anthropic",
            "llm_model": "claude-3-5-sonnet-20241022",
            "llm_api_key": anthropic_key
        }
        print(f"🤖 LLM: Anthropic Claude 3.5 Sonnet")
    elif requesty_key:
        llm_config = {
            "llm_provider": "requesty",
            "llm_model": "anthropic/claude-3-5-sonnet-20241022",
            "llm_api_key": requesty_key
        }
        print(f"🤖 LLM: Requesty with Claude 3.5 Sonnet")
    else:
        print("❌ No LLM API key found in environment variables")
        print("   Please set one of: OPENAI_API_KEY, ANTHROPIC_API_KEY, or REQUESTY_API_KEY")
        return None
    
    print(f"🔗 Neo4j: {neo4j_uri}")
    
    return {
        "neo4j_uri": neo4j_uri,
        "neo4j_user": neo4j_user,
        "neo4j_password": neo4j_password,
        **llm_config
    }

def clear_previous_data(config, file_id):
    """Clear any previous data for this file from Neo4j."""
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            config["neo4j_uri"], 
            auth=(config["neo4j_user"], config["neo4j_password"])
        )
        
        with driver.session() as session:
            # Remove previous data for this file
            session.run('MATCH (f:File {id: $file_id}) DETACH DELETE f', file_id=file_id)
            session.run('MATCH (c:Chunk) WHERE c.id STARTS WITH $file_id DETACH DELETE c', file_id=file_id)
            
        driver.close()
        print(f"🧹 Cleared previous data for {file_id}")
        return True
        
    except Exception as e:
        print(f"⚠️  Could not clear previous data: {e}")
        return True  # Continue anyway

def process_pdf_document(config, pdf_path: str, file_id: str):
    """Process PDF document through enterprise_kg_minimal pipeline."""
    
    # Extract PDF content
    print(f"\n📄 Extracting content from PDF...")
    content = extract_pdf_content(pdf_path)
    
    if not content:
        return None
    
    print(f"   Content length: {len(content)} characters")
    print(f"   Preview: {content[:200]}...")
    
    # Process through enterprise_kg_minimal
    print(f"\n🔄 Processing document with enterprise_kg_minimal...")
    
    try:
        from enterprise_kg_minimal import process_document
        
        result = process_document(
            file_id=file_id,
            file_content=content,
            neo4j_uri=config["neo4j_uri"],
            neo4j_user=config["neo4j_user"],
            neo4j_password=config["neo4j_password"],
            llm_provider=config["llm_provider"],
            llm_model=config["llm_model"],
            llm_api_key=config["llm_api_key"],
            chunking_strategy="hybrid",
            chunk_size=1000,
            chunk_overlap=200
        )
        
        return result
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_graph_connectivity(config, file_id: str):
    """Analyze the connectivity of the created knowledge graph."""
    
    print(f"\n🔍 Analyzing graph connectivity...")
    
    try:
        from enterprise_kg_minimal.storage.neo4j_client import Neo4jClient, Neo4jConnection
        from enterprise_kg_minimal.utils.graph_analyzer import GraphConnectivityAnalyzer
        
        # Create Neo4j client
        neo4j_conn = Neo4jConnection(
            uri=config["neo4j_uri"],
            user=config["neo4j_user"],
            password=config["neo4j_password"]
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        # Create analyzer
        analyzer = GraphConnectivityAnalyzer(neo4j_client)
        
        # Analyze connectivity
        result = analyzer.analyze_graph_connectivity(file_id=file_id)
        
        # Print detailed report
        analyzer.print_connectivity_report(result, file_id)
        
        return result
        
    except Exception as e:
        print(f"❌ Connectivity analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def display_processing_results(result):
    """Display document processing results."""
    
    if not result:
        print("❌ No processing results to display")
        return False
    
    print(f"\n📊 Document Processing Results:")
    print("=" * 50)
    
    if result["success"]:
        print(f"✅ SUCCESS!")
        print(f"   📄 File ID: {result['file_id']}")
        print(f"   🧩 Chunks created: {result['chunks_created']}")
        print(f"   ✅ Chunks processed: {result['chunks_processed']}")
        print(f"   ❌ Chunks failed: {result['chunks_failed']}")
        print(f"   👥 Total entities: {result['total_entities']}")
        print(f"   🔗 Total relationships: {result['total_relationships']}")
        
        # Calculate success rate
        success_rate = (result['chunks_processed'] / result['chunks_created']) * 100 if result['chunks_created'] > 0 else 0
        print(f"   📈 Success Rate: {success_rate:.1f}%")
        
        return True
    else:
        print(f"❌ FAILED: {result['error']}")
        return False

def show_neo4j_queries(file_id):
    """Show useful Neo4j queries for exploring the results."""
    
    print(f"\n🔍 Neo4j Browser Queries:")
    print("=" * 50)
    print("Copy and paste these queries into Neo4j Browser:\n")
    
    print("1️⃣ View complete graph structure:")
    print(f'MATCH (f:File {{id: "{file_id}"}})-[:CONTAINS]->(c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)')
    print('RETURN f, c, e')
    print()
    
    print("2️⃣ Check connectivity paths:")
    print('MATCH path = (n1)-[*]-(n2)')
    print('WHERE n1 <> n2')
    print('RETURN length(path) as path_length, count(*) as count')
    print('ORDER BY path_length')
    print()
    
    print("3️⃣ Find isolated nodes:")
    print('MATCH (n)')
    print('WHERE NOT (n)--() ')
    print('RETURN n')

def main():
    """Main function to run PDF analysis and connectivity check."""
    
    print("🧪 PDF Document Analysis with Connectivity Check")
    print("=" * 60)
    
    # Find PDF file
    pdf_path = "enterprise_kg_minimal/documents/Culture Values - Rapid.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return False
    
    print(f"📄 PDF File: {pdf_path}")
    
    # Setup configuration
    config = setup_configuration()
    if not config:
        return False
    
    # Generate file ID
    file_id = "culture_values_rapid"
    
    # Clear previous data
    clear_previous_data(config, file_id)
    
    # Process PDF document
    processing_result = process_pdf_document(config, pdf_path, file_id)
    
    # Display processing results
    processing_success = display_processing_results(processing_result)
    
    if processing_success:
        # Analyze graph connectivity
        connectivity_result = analyze_graph_connectivity(config, file_id)
        
        if connectivity_result:
            # Show Neo4j queries
            show_neo4j_queries(file_id)
            
            print(f"\n🎉 Analysis completed successfully!")
            print(f"   Graph connectivity: {'✅ CONNECTED' if connectivity_result.is_connected else '❌ NOT CONNECTED'}")
            print(f"   Open Neo4j Browser to explore the graph structure")
        else:
            print(f"\n💥 Connectivity analysis failed")
    else:
        print(f"\n💥 Document processing failed")
    
    return processing_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
