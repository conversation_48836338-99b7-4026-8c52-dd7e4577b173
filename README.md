# Enterprise Knowledge Graph (EnterpriseKG)

A custom enterprise data management system built on top of CocoIndex for extracting entities and relationships from enterprise documents and building knowledge graphs.

## Features

- **Custom Entity & Relationship Extraction**: Define your own entity types and relationship patterns
- **LLM-Powered Processing**: Uses various LLM providers for intelligent extraction
- **Vector Database Integration**: Pinecone support for semantic search
- **Graph Database Integration**: Neo4j support for knowledge graph storage
- **Flexible Schema Definition**: Easy-to-extend entity and relationship definitions

## Architecture

```
enterprise_kg/
├── __init__.py
├── constants/
│   ├── __init__.py
│   ├── entities.py          # Entity type definitions
│   ├── relationships.py     # Relationship type definitions
│   └── schemas.py          # Data schemas
├── core/
│   ├── __init__.py
│   ├── extractor.py        # Main extraction logic
│   ├── summarizer.py       # Document summarization
│   └── pipeline.py         # Processing pipeline
├── storage/
│   ├── __init__.py
│   ├── pinecone_client.py  # Pinecone vector DB client
│   └── neo4j_client.py     # Neo4j graph DB client
├── flows/
│   ├── __init__.py
│   └── enterprise_flow.py  # Main processing flow
└── utils/
    ├── __init__.py
    └── helpers.py          # Utility functions

## Quick Start

1. Install dependencies
2. Configure your LLM provider (OpenAI, Anthropic, etc.)
3. Set up Pinecone and Neo4j connections
4. Define your entity and relationship types
5. Run the extraction pipeline

## Usage

```python
import enterprise_kg

# Create and run the enterprise knowledge graph flow
flow = enterprise_kg.create_enterprise_flow()
flow.run()
```
