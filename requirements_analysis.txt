# Requirements for PDF Analysis and Graph Connectivity Check
# Install with: pip install -r requirements_analysis.txt

# Core dependencies from enterprise_kg_minimal
neo4j>=5.0.0
python-dotenv>=1.0.0

# LLM providers (install at least one)
openai>=1.0.0          # For OpenAI GPT models
anthropic>=0.7.0       # For Anthropic Claude models

# PDF processing
pypdf>=3.0.0          # For PDF text extraction

# Optional: Alternative PDF processing libraries
# PyPDF2>=3.0.0       # Alternative PDF library
# pdfplumber>=0.9.0    # More advanced PDF processing
