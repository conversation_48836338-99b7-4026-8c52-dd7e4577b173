#!/usr/bin/env python3
"""
Clear Neo4j Graph Database

This script clears all nodes and relationships from the Neo4j database.
Use this to start fresh before running new document processing.

Usage:
    python clear_neo4j_graph.py
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def clear_neo4j_database():
    """Clear all data from Neo4j database."""
    
    # Get Neo4j configuration
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    print("🧹 Clearing Neo4j Database")
    print("=" * 40)
    print(f"URI: {neo4j_uri}")
    print(f"User: {neo4j_user}")
    
    try:
        from neo4j import GraphDatabase
        
        # Connect to Neo4j
        driver = GraphDatabase.driver(
            neo4j_uri,
            auth=(neo4j_user, neo4j_password)
        )
        
        with driver.session() as session:
            # Get current counts
            print("\n📊 Current Database State:")
            
            # Count nodes
            node_result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = node_result.single()["node_count"]
            print(f"   Nodes: {node_count}")
            
            # Count relationships
            rel_result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
            rel_count = rel_result.single()["rel_count"]
            print(f"   Relationships: {rel_count}")
            
            if node_count == 0 and rel_count == 0:
                print("\n✅ Database is already empty!")
                driver.close()
                return True
            
            # Confirm deletion
            print(f"\n⚠️  This will delete ALL {node_count} nodes and {rel_count} relationships!")
            confirm = input("Are you sure you want to continue? (yes/no): ").lower().strip()
            
            if confirm not in ['yes', 'y']:
                print("❌ Operation cancelled")
                driver.close()
                return False
            
            print("\n🗑️  Deleting all data...")
            
            # Delete all nodes and relationships
            session.run("MATCH (n) DETACH DELETE n")
            
            # Verify deletion
            final_node_result = session.run("MATCH (n) RETURN count(n) as node_count")
            final_node_count = final_node_result.single()["node_count"]
            
            final_rel_result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
            final_rel_count = final_rel_result.single()["rel_count"]
            
            print(f"\n📊 Final Database State:")
            print(f"   Nodes: {final_node_count}")
            print(f"   Relationships: {final_rel_count}")
            
            if final_node_count == 0 and final_rel_count == 0:
                print("\n✅ Database cleared successfully!")
                success = True
            else:
                print("\n❌ Some data may still remain")
                success = False
        
        driver.close()
        return success
        
    except ImportError:
        print("❌ neo4j package not installed. Install with: pip install neo4j")
        return False
    except Exception as e:
        print(f"❌ Error clearing database: {e}")
        return False

def main():
    """Main function."""
    
    print("🧹 Neo4j Database Cleaner")
    print("=" * 50)
    
    success = clear_neo4j_database()
    
    if success:
        print("\n🎉 Ready for fresh document processing!")
        print("   Run 'python process_pdf_document.py' to process the PDF")
    else:
        print("\n💥 Failed to clear database")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
