"""
Enterprise KG Minimal Package

A standalone package for enterprise knowledge graph processing that creates
chunk-based knowledge graphs from documents.

This package provides:
- Document chunking and processing
- Entity and relationship extraction per chunk
- Neo4j knowledge graph storage with chunk structure
- File → Chunks → Chunk Graphs architecture

Usage:
    from enterprise_kg_minimal import process_document

    result = process_document(
        file_id="doc_123",
        file_content="Your document content here...",
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="password"
    )
"""

from .core.document_processor import process_document

__version__ = "2.0.0"
__all__ = [
    "process_document"
]
