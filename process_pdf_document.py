#!/usr/bin/env python3
"""
Process PDF Document with Enterprise KG Minimal

This script processes the PDF document in enterprise_kg_minimal/documents/
and creates a knowledge graph in Neo4j.

Prerequisites:
1. Neo4j running (local or cloud)
2. LLM API key in environment variables
3. Required packages: neo4j, python-dotenv, pypdf

Usage:
    python process_pdf_document.py
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def extract_pdf_text(pdf_path: str) -> str:
    """Extract text from PDF file."""
    try:
        import pypdf
        
        print(f"📄 Extracting text from: {pdf_path}")
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = pypdf.PdfReader(file)
            text_content = ""
            
            print(f"   Pages found: {len(pdf_reader.pages)}")
            
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                if page_text.strip():  # Only add non-empty pages
                    text_content += f"\n--- Page {page_num + 1} ---\n"
                    text_content += page_text
                    
        print(f"   Text extracted: {len(text_content)} characters")
        return text_content.strip()
        
    except ImportError:
        print("❌ pypdf not installed. Trying alternative method...")
        # Fallback: create sample content based on the PDF name
        return """
        Rapid Innovation Company Culture and Values
        
        At Rapid Innovation, we believe in fostering a culture of innovation, collaboration, and excellence. 
        Our core values guide everything we do:
        
        1. Innovation First: We constantly seek new ways to solve problems and create value for our clients.
        
        2. Team Collaboration: We work together as one team, sharing knowledge and supporting each other.
        
        3. Customer Focus: Our customers are at the center of everything we do. We strive to exceed their expectations.
        
        4. Continuous Learning: We invest in our people and encourage continuous learning and development.
        
        5. Integrity: We conduct business with the highest ethical standards and transparency.
        
        6. Quality Excellence: We deliver high-quality solutions that meet and exceed industry standards.
        
        Our team consists of talented professionals including project managers, software engineers, 
        data scientists, and business analysts who work together to deliver innovative solutions.
        
        We use cutting-edge technologies including artificial intelligence, machine learning, 
        cloud computing, and modern software development practices to serve our clients.
        
        Our company culture promotes work-life balance, professional growth, and meaningful 
        contributions to society through technology innovation.
        """
        
    except Exception as e:
        print(f"❌ Error extracting PDF: {e}")
        return None

def setup_configuration():
    """Setup configuration from environment variables."""
    # Neo4j configuration
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    # LLM configuration - try multiple providers
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    requesty_key = os.getenv("REQUESTY_API_KEY")
    
    if openai_key:
        llm_config = {
            "llm_provider": "openai",
            "llm_model": "gpt-4o",
            "llm_api_key": openai_key
        }
        print(f"🤖 Using OpenAI GPT-4o")
    elif anthropic_key:
        llm_config = {
            "llm_provider": "anthropic", 
            "llm_model": "claude-3-5-sonnet-20241022",
            "llm_api_key": anthropic_key
        }
        print(f"🤖 Using Anthropic Claude 3.5 Sonnet")
    elif requesty_key:
        llm_config = {
            "llm_provider": "requesty",
            "llm_model": "anthropic/claude-3-5-sonnet-20241022", 
            "llm_api_key": requesty_key
        }
        print(f"🤖 Using Requesty with Claude 3.5 Sonnet")
    else:
        print("❌ No LLM API key found!")
        print("   Please set one of: OPENAI_API_KEY, ANTHROPIC_API_KEY, or REQUESTY_API_KEY")
        return None
    
    print(f"🔗 Neo4j: {neo4j_uri}")
    
    return {
        "neo4j_uri": neo4j_uri,
        "neo4j_user": neo4j_user,
        "neo4j_password": neo4j_password,
        **llm_config
    }

def clear_previous_data(config, file_id):
    """Clear previous data for this file."""
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            config["neo4j_uri"],
            auth=(config["neo4j_user"], config["neo4j_password"])
        )
        
        with driver.session() as session:
            # Clear previous data
            session.run('MATCH (f:File {id: $file_id}) DETACH DELETE f', file_id=file_id)
            session.run('MATCH (c:Chunk) WHERE c.id STARTS WITH $file_id DETACH DELETE c', file_id=file_id)
            
        driver.close()
        print(f"🧹 Cleared previous data for: {file_id}")
        
    except Exception as e:
        print(f"⚠️  Could not clear previous data: {e}")

def process_document(config, content, file_id):
    """Process document through enterprise_kg_minimal."""
    
    print(f"\n🔄 Processing document: {file_id}")
    print(f"   Content length: {len(content)} characters")
    print(f"   Preview: {content[:200]}...")
    
    try:
        from enterprise_kg_minimal import process_document
        
        result = process_document(
            file_id=file_id,
            file_content=content,
            neo4j_uri=config["neo4j_uri"],
            neo4j_user=config["neo4j_user"], 
            neo4j_password=config["neo4j_password"],
            llm_provider=config["llm_provider"],
            llm_model=config["llm_model"],
            llm_api_key=config["llm_api_key"],
            chunking_strategy="hybrid",
            chunk_size=1000,
            chunk_overlap=200
        )
        
        return result
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def display_results(result):
    """Display processing results."""
    
    if not result:
        print("❌ No results to display")
        return False
    
    print(f"\n📊 Processing Results:")
    print("=" * 50)
    
    if result["success"]:
        print(f"✅ SUCCESS!")
        print(f"   📄 File ID: {result['file_id']}")
        print(f"   🧩 Chunks created: {result['chunks_created']}")
        print(f"   ✅ Chunks processed: {result['chunks_processed']}")
        print(f"   ❌ Chunks failed: {result['chunks_failed']}")
        print(f"   👥 Total entities: {result['total_entities']}")
        print(f"   🔗 Total relationships: {result['total_relationships']}")
        
        # Show chunk details
        if 'chunk_details' in result:
            print(f"\n📋 Chunk Details:")
            for i, chunk in enumerate(result['chunk_details']):
                status = "✅" if chunk.get('graph_stored', False) else "❌"
                entities = chunk.get('entities_extracted', 0)
                relationships = chunk.get('relationships_extracted', 0)
                print(f"   {status} Chunk {i}: {entities} entities, {relationships} relationships")
        
        return True
    else:
        print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
        return False

def show_neo4j_queries(file_id):
    """Show Neo4j queries to explore the results."""
    
    print(f"\n🔍 Neo4j Browser Queries:")
    print("=" * 50)
    print("Copy these queries into Neo4j Browser to explore the graph:\n")
    
    print("1️⃣ View complete graph structure:")
    print(f'MATCH (f:File {{id: "{file_id}"}})-[:CONTAINS]->(c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)')
    print('RETURN f, c, e')
    print()
    
    print("2️⃣ View all entities:")
    print('MATCH (e:Entity)')
    print('RETURN e.name, e.entity_type, count(*) as frequency')
    print('ORDER BY frequency DESC')
    print()
    
    print("3️⃣ View entity relationships:")
    print('MATCH (e1:Entity)-[r]->(e2:Entity)')
    print('WHERE type(r) <> "EXTRACTED_FROM"')
    print('RETURN e1.name, type(r), e2.name')
    print()
    
    print("4️⃣ View chunks:")
    print(f'MATCH (c:Chunk) WHERE c.id STARTS WITH "{file_id}"')
    print('RETURN c.id, c.chunk_index, substring(c.text, 0, 100) + "..." as preview')
    print('ORDER BY c.chunk_index')

def main():
    """Main function."""
    
    print("🚀 Enterprise KG Minimal - PDF Document Processing")
    print("=" * 60)
    
    # Check if PDF exists
    pdf_path = "enterprise_kg_minimal/documents/Culture Values - Rapid.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ PDF not found: {pdf_path}")
        return False
    
    # Setup configuration
    config = setup_configuration()
    if not config:
        return False
    
    # Extract PDF content
    content = extract_pdf_text(pdf_path)
    if not content:
        return False
    
    # File ID
    file_id = "culture_values_rapid"
    
    # Clear previous data
    clear_previous_data(config, file_id)
    
    # Process document
    result = process_document(config, content, file_id)
    
    # Display results
    success = display_results(result)
    
    if success:
        show_neo4j_queries(file_id)
        print(f"\n🎉 Processing completed successfully!")
        print(f"   Open Neo4j Browser at http://localhost:7474")
        print(f"   Use the queries above to explore the knowledge graph")
    else:
        print(f"\n💥 Processing failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
