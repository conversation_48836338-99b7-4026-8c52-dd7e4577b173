# Pipeline State Module Explanation

This document provides a detailed explanation of the `pipeline_state` module, its overall process, and the responsibilities of its database tables and columns as defined in `pipeline_state/models.py`.

## I. Overview and Purpose

The `pipeline_state` module serves as a comprehensive system for managing and tracking the state of an enterprise knowledge graph (KG) processing pipeline. Its primary purpose is to orchestrate the various stages involved in transforming raw data from different sources into a structured knowledge graph, while maintaining robust state information, enabling resumable processing, and providing monitoring capabilities.

Key functionalities include:

*   **Multi-stage Pipeline Tracking**: Monitors each defined stage of the processing pipeline.
*   **State Persistence**: Uses a PostgreSQL database to reliably store the state of all pipeline components and operations.
*   **Orchestration**: Manages the execution flow of the pipeline, including handling dependencies between stages.
*   **Integration**: Works in conjunction with the `enterprise_kg_minimal` module for core document processing and graph generation tasks.
*   **Error Handling and Recovery**: Tracks errors and supports retrying failed operations.
*   **Status Monitoring**: Offers capabilities to track the status and performance of the pipeline.

## II. Core Process Flow

The `pipeline_state` module, primarily through the `PipelineOrchestrator`, manages the following general process:

1.  **Initialization**:
    *   The `PipelineOrchestrator` is initialized with configurations, including PostgreSQL database connection details.
    *   It sets up a `DatabaseManager` to interact with PostgreSQL and a `StateManager` to handle state-related logic.
    *   Database tables (defined in `models.py`) are created if they don't exist, and default pipeline stages are initialized.

2.  **Organization Processing (`process_organization` method in `PipelineOrchestrator`):**
    *   **Organization Setup**: An `Organization` entity is created or updated in the database, storing its metadata and configurations (Neo4j, Pinecone, LLM).
    *   **Data Source Setup**: `DataSource` entities are registered for the organization, specifying the type (e.g., files, Slack, Jira) and their specific configurations.
    *   **File Collection**: For each data source, files/content are collected. For instance, for a 'files' data source, it might scan a directory. Each discovered file is registered as a `File` entity in the database, storing its metadata and content.
    *   **Job Creation**: For each registered `File`, a `ProcessingJob` is created. This job represents the entire pipeline execution for that specific file.
    *   **Stage Execution Setup**: For each `ProcessingJob`, corresponding `StageExecution` records are created for all defined `PipelineStage`s, initially in a pending state.

3.  **Pipeline Execution (handled by `_execute_single_job` and stage-specific methods in `PipelineOrchestrator`):**
    *   Each `ProcessingJob` goes through a sequence of predefined `PipelineStage`s.
    *   The orchestrator executes these stages in order, respecting dependencies.
    *   For each stage, the `StateManager` updates the status of the `StageExecution` record (e.g., running, completed, failed).
    *   The key stages (as per `README.md` and `database.py`) are:
        1.  **Data Source Connection**: (Primarily handled during file collection) Ensures connection to data sources and gathers content.
        2.  **File Attachment**: Attaches files to the organization in Neo4j (e.g., creating an `Organization` node and a `CONTAINS` relationship to a `File` node).
        3.  **Document Processing**: This is where `enterprise_kg_minimal.process_document` is invoked. It takes the file content, performs chunking, entity/relationship extraction, and initial graph building within the scope of that document. Results like chunks created are stored.
        4.  **Graph Generation**: (Largely covered by `enterprise_kg_minimal`) Ensures the chunk-based graphs are correctly formed and linked to the file node in Neo4j.
        5.  **Vector Storage**: (Optional, if Pinecone is configured) Involves generating embeddings for chunks and storing them in a vector database like Pinecone for semantic search capabilities.
    *   The status of the overall `ProcessingJob` is updated based on the progress and success/failure of its stages.

4.  **State Management and Monitoring**:
    *   The `StateManager` is used throughout the process to update and query the state of organizations, files, jobs, and stage executions in the PostgreSQL database.
    *   The `StatusTracker` uses the data in PostgreSQL to provide overviews, dashboards, performance metrics, and reports on the pipeline's health and progress.

## III. Database Schema (`pipeline_state/models.py`)

The following tables are defined in `pipeline_state/models.py` using SQLAlchemy to manage the pipeline's state in a PostgreSQL database.

### Enums

These enums define controlled vocabularies for status, types, etc.

*   **`ProcessingStatus(Enum)`**: Defines the possible statuses for jobs and stages.
    *   `PENDING`: Task is waiting to be processed.
    *   `RUNNING`: Task is currently being processed.
    *   `COMPLETED`: Task finished successfully.
    *   `FAILED`: Task encountered an error and did not complete.
    *   `RETRYING`: Task is being retried after a failure.
    *   `CANCELLED`: Task was cancelled.
*   **`DataSourceType(Enum)`**: Defines the types of data sources the pipeline can connect to.
    *   `SLACK`, `JIRA`, `FILES`, `CONFLUENCE`, `SHAREPOINT`, `GOOGLE_DRIVE`.
*   **`StageType(Enum)`**: Defines the types for different stages in the pipeline.
    *   `DATA_SOURCE_CONNECTION`, `FILE_ATTACHMENT`, `DOCUMENT_PROCESSING`, `GRAPH_GENERATION`, `VECTOR_STORAGE`.

### Tables

1.  **`organizations` (Class: `Organization`)**
    *   **Purpose**: Stores information about different organizations whose data is being processed. Each organization can have its own set of data sources and configurations.
    *   **Columns**:
        *   `id (String, PK)`: Unique identifier for the organization (e.g., "rapid_innovation").
        *   `name (String, Not Null)`: Name of the organization.
        *   `description (Text)`: Optional textual description of the organization.
        *   `created_at (DateTime)`: Timestamp of when the organization record was created.
        *   `updated_at (DateTime)`: Timestamp of the last update to the organization record.
        *   `neo4j_config (JSON)`: JSON object storing Neo4j connection details (URI, user, password, database) specific to this organization.
        *   `pinecone_config (JSON)`: JSON object storing Pinecone configuration (API key, environment, index name) if used by this organization.
        *   `llm_config (JSON)`: JSON object storing LLM provider details (provider, model, API key) for this organization.
    *   **Relationships**:
        *   `data_sources`: One-to-many with `DataSource`.
        *   `files`: One-to-many with `File`.
        *   `processing_jobs`: One-to-many with `ProcessingJob`.

2.  **`data_sources` (Class: `DataSource`)**
    *   **Purpose**: Stores details about the specific data sources linked to an organization (e.g., a particular Slack workspace, a specific file directory).
    *   **Columns**:
        *   `id (String, PK)`: Unique identifier for the data source (e.g., "rapid_innovation_files_0").
        *   `organization_id (String, FK -> organizations.id, Not Null)`: Links to the parent organization.
        *   `name (String, Not Null)`: Name of the data source (e.g., "Document Repository").
        *   `type (SQLEnum(DataSourceType), Not Null)`: Type of the data source (e.g., `FILES`, `SLACK`).
        *   `config (JSON)`: JSON object for source-specific configuration (e.g., for `FILES`, this might contain `{"path": "documents"}`).
        *   `credentials (JSON)`: JSON object for storing (ideally encrypted or referencing a secrets manager) credentials needed to access the data source.
        *   `status (SQLEnum(ProcessingStatus))`: Current processing status related to this data source (e.g., for sync operations).
        *   `last_sync (DateTime)`: Timestamp of the last successful synchronization with this data source.
        *   `next_sync (DateTime)`: Timestamp for the next scheduled synchronization.
        *   `created_at (DateTime)`: Record creation timestamp.
        *   `updated_at (DateTime)`: Record last update timestamp.
    *   **Relationships**:
        *   `organization`: Many-to-one with `Organization`.
        *   `files`: One-to-many with `File` (files originating from this data source).

3.  **`files` (Class: `File`)**
    *   **Purpose**: Represents an individual file or piece of content collected from a data source that needs to be processed by the pipeline.
    *   **Columns**:
        *   `id (String, PK)`: Unique identifier for the file (e.g., "rapid_innovation_files_0_uuid").
        *   `organization_id (String, FK -> organizations.id, Not Null)`: Links to the organization this file belongs to.
        *   `data_source_id (String, FK -> data_sources.id, Not Null)`: Links to the data source from which this file originated.
        *   `filename (String, Not Null)`: Original name of the file.
        *   `file_path (Text)`: Path to the file if applicable (e.g., for filesystem sources).
        *   `file_size (Integer)`: Size of the file in bytes.
        *   `content_type (String)`: MIME type of the file (e.g., "text/plain", "application/pdf").
        *   `content_hash (String)`: Hash (e.g., SHA-256) of the file content to detect changes.
        *   `content (Text)`: Actual content of the file, especially for text-based files or extracted text from binaries.
        *   `metadata (JSON)`: JSON object for storing any additional file-specific metadata.
        *   `status (SQLEnum(ProcessingStatus))`: Overall processing status of this file through the pipeline.
        *   `neo4j_node_created (Boolean)`: Flag indicating if the primary Neo4j node for this file has been created.
        *   `chunks_created (Integer)`: Number of chunks generated from this file during document processing.
        *   `embeddings_created (Boolean)`: Flag indicating if embeddings for this file's chunks have been generated and stored.
        *   `created_at (DateTime)`: Record creation timestamp.
        *   `updated_at (DateTime)`: Record last update timestamp.
        *   `last_processed (DateTime)`: Timestamp of when this file was last processed by the pipeline.
    *   **Relationships**:
        *   `organization`: Many-to-one with `Organization`.
        *   `data_source`: Many-to-one with `DataSource`.
        *   `processing_jobs`: One-to-many with `ProcessingJob` (a file usually has one main processing job).

4.  **`processing_jobs` (Class: `ProcessingJob`)**
    *   **Purpose**: Tracks an individual run of the entire pipeline for a specific file.
    *   **Columns**:
        *   `id (String, PK)`: Unique identifier for the processing job (UUID).
        *   `organization_id (String, FK -> organizations.id, Not Null)`: Links to the organization.
        *   `file_id (String, FK -> files.id, Not Null)`: Links to the file being processed by this job.
        *   `job_type (String, Not Null)`: Type of job (e.g., "full_pipeline", "retry_stage") indicating the scope of processing.
        *   `config (JSON)`: JSON object for job-specific configuration overrides or parameters.
        *   `status (SQLEnum(ProcessingStatus))`: Overall status of this processing job.
        *   `current_stage (SQLEnum(StageType))`: The `StageType` enum value for this stage.
        *   `progress_percentage (Integer)`: An estimate of the job's completion progress (0-100).
        *   `result (JSON)`: JSON object to store the final results or summary of the job.
        *   `error_message (Text)`: Stores the error message if the job failed.
        *   `created_at (DateTime)`: Job creation timestamp.
        *   `started_at (DateTime)`: Timestamp when the job processing actually started.
        *   `completed_at (DateTime)`: Timestamp when the job finished (either completed or failed).
    *   **Relationships**:
        *   `organization`: Many-to-one with `Organization`.
        *   `file`: Many-to-one with `File`.
        *   `stage_executions`: One-to-many with `StageExecution` (tracking each stage within this job).

5.  **`pipeline_stages` (Class: `PipelineStage`)**
    *   **Purpose**: Defines the blueprint for each stage in the processing pipeline. These are typically static definitions.
    *   **Columns**:
        *   `id (Integer, PK)`: Unique integer identifier for the stage definition.
        *   `name (String, Not Null, Unique)`: Human-readable name of the stage (e.g., "Document Processing").
        *   `type (SQLEnum(StageType), Not Null)`: The `StageType` enum value for this stage.
        *   `description (Text)`: Optional description of what the stage does.
        *   `order_index (Integer, Not Null)`: Defines the execution order of the stage in the pipeline.
        *   `is_parallel (Boolean)`: Flag indicating if instances of this stage (for different jobs) can be run in parallel.
        *   `retry_count (Integer)`: Default number of retries for this stage in case of failure.
        *   `timeout_seconds (Integer)`: Default timeout for this stage's execution.
        *   `depends_on (JSON)`: A list of `PipelineStage.id`s that this stage depends on. Processing for this stage will only begin after its dependencies are complete.
        *   `created_at (DateTime)`: Record creation timestamp.

6.  **`stage_executions` (Class: `StageExecution`)**
    *   **Purpose**: Tracks the execution of a specific `PipelineStage` for a particular `ProcessingJob`.
    *   **Columns**:
        *   `id (String, PK)`: Unique identifier for this specific stage execution instance (UUID).
        *   `job_id (String, FK -> processing_jobs.id, Not Null)`: Links to the parent `ProcessingJob`.
        *   `stage_id (Integer, FK -> pipeline_stages.id, Not Null)`: Links to the `PipelineStage` definition being executed.
        *   `status (SQLEnum(ProcessingStatus))`: Current status of this stage execution.
        *   `attempt_number (Integer)`: The attempt number for this stage execution (e.g., 1 for the first try, 2 for a retry).
        *   `result (JSON)`: JSON object to store results specific to this stage's execution.
        *   `error_message (Text)`: Error message if this stage execution failed.
        *   `logs (Text)`: Stores logs or detailed output from this stage execution.
        *   `created_at (DateTime)`: Record creation timestamp.
        *   `started_at (DateTime)`: Timestamp when this stage execution started.
        *   `completed_at (DateTime)`: Timestamp when this stage execution finished.
    *   **Relationships**:
        *   `job`: Many-to-one with `ProcessingJob`.
        *   `stage`: Many-to-one with `PipelineStage`.
        *   `error_logs`: One-to-many with `ErrorLog`.

7.  **`error_logs` (Class: `ErrorLog`)**
    *   **Purpose**: Stores detailed information about errors that occur during `StageExecution`.
    *   **Columns**:
        *   `id (String, PK)`: Unique identifier for the error log entry (UUID).
        *   `stage_execution_id (String, FK -> stage_executions.id)`: Links to the `StageExecution` during which the error occurred.
        *   `error_type (String)`: The type or class of the error (e.g., "ValueError", "APITimeoutError").
        *   `error_message (Text)`: The main error message.
        *   `stack_trace (Text)`: The full stack trace of the error, if available.
        *   `context (JSON)`: JSON object storing additional context relevant to the error (e.g., parameters, state at time of error).
        *   `is_recoverable (Boolean)`: Flag indicating if the error is considered recoverable (e.g., a transient network issue).
        *   `recovery_suggestion (Text)`: Textual suggestion on how the error might be recovered or resolved.
        *   `created_at (DateTime)`: Timestamp when the error was logged.
    *   **Relationships**:
        *   `stage_execution`: Many-to-one with `StageExecution`.

### Indexes

The `models.py` file also defines several indexes to improve query performance on frequently queried columns/combinations:
*   `idx_files_org_status`: On `files` table for `organization_id` and `status`.
*   `idx_jobs_org_status`: On `processing_jobs` table for `organization_id` and `status`.
*   `idx_stage_exec_job_stage`: On `stage_executions` table for `job_id` and `stage_id`.
*   `idx_error_logs_stage`: On `error_logs` table for `stage_execution_id`.

This detailed structure allows the `pipeline_state` module to effectively manage, track, and report on the complex process of enterprise knowledge graph creation.